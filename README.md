# Upzi Opportunity Creation

A FastAPI-based microservice for generating, parsing, and managing job postings and career events using AI-powered language models.

## Table of Contents

- [Installation](#installation)
- [Project Structure](#project-structure)
- [API Endpoints](#api-endpoints)
- [Configuration](#configuration)
- [Development](#development)

## Installation

### Prerequisites

- Python 3.12 or higher
- Poetry (for dependency management)
- Redis (for caching)

### Setup Instructions

1. Clone the repository:
```bash
git clone <repository-url>
cd upzi-opportunity-creation
```

2. Install dependencies using Poetry:
```bash
poetry install
```

3. Install Playwright browsers (required for web scraping):
```bash
poetry run playwright install
```

4. Create a `.env` file in the project root with the following variables:
```env
# Application Settings
APP_HOST=0.0.0.0
APP_PORT=8080
APP_ENVIRONMENT=dev

# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379

# Sentry Configuration (optional)
SENTRY_DSN=your_sentry_dsn

# Langfuse Configuration (optional)
LANGFUSE_HOST=your_langfuse_host
LANGFUSE_PUBLIC_KEY=your_public_key
LANGFUSE_SECRET_KEY=your_secret_key
```

5. Run the application:
```bash
poetry run python main.py
```

The API will be available at `http://localhost:8080`.

### Using Docker (Alternative)

If you prefer using Docker, create a `Dockerfile`:
```dockerfile
FROM python:3.12-slim

WORKDIR /app

RUN pip install poetry

COPY pyproject.toml poetry.lock ./
RUN poetry config virtualenvs.create false && poetry install --no-dev

COPY . .

CMD ["python", "main.py"]
```

## Project Structure

```
upzi-opportunity-creation/
├── app/
│   ├── api/
│   │   └── routers/
│   │       ├── health.py              # Health check endpoint
│   │       ├── utils.py               # Router utilities
│   │       ├── v1/                    # Version 1 API endpoints
│   │       │   ├── career_event/
│   │       │   │   ├── generate.py    # Generate career events
│   │       │   │   └── parse.py       # Parse career event data
│   │       │   └── job_post/
│   │       │       ├── edit.py        # Edit job postings
│   │       │       ├── generate.py    # Generate job postings
│   │       │       ├── parse.py       # Parse job descriptions
│   │       │       └── screening_questions.py  # Generate screening questions
│   │       └── v2/                    # Version 2 API endpoints
│   │           └── job_post/
│   │               ├── generate.py    # Enhanced job generation
│   │               └── parse.py       # Enhanced job parsing
│   │
│   ├── core/                          # Core business logic
│   │   ├── configuration.py           # App configuration
│   │   ├── tools.py                   # Shared tools and utilities
│   │   ├── utils.py                   # Core utilities
│   │   ├── career_event/              # Career event processing
│   │   │   ├── generate/              # Event generation workflow
│   │   │   └── parse/                 # Event parsing workflow
│   │   └── job_post/                  # Job post processing
│   │       ├── edit/                  # Job editing workflow
│   │       ├── generate/              # Job generation workflow
│   │       ├── generate_v2/           # Enhanced generation workflow
│   │       ├── parse/                 # Job parsing workflow
│   │       ├── parse_v2/              # Enhanced parsing workflow
│   │       ├── screening_questions/   # Screening questions generation
│   │       ├── suggest_content/       # Content suggestions
│   │       ├── suggest_job_function/  # Job function suggestions
│   │       └── suggest_skills/        # Skills suggestions
│   │
│   ├── exceptions/                    # Custom exceptions
│   │   └── custom_error.py
│   │
│   ├── handlers/                      # Request handlers
│   │   ├── edit.py
│   │   ├── generate.py
│   │   ├── parse.py
│   │   └── screening_questions.py
│   │
│   ├── helpers/                       # Helper functions
│   │   ├── get_content_from_file.py   # File content extraction
│   │   ├── get_content_from_google_docs.py  # Google Docs integration
│   │   ├── get_content_from_url.py    # URL content extraction
│   │   ├── get_location.py            # Location processing
│   │   ├── get_meta_job_post.py       # Job metadata extraction
│   │   ├── get_suggest_salary.py      # Salary suggestions
│   │   ├── hash_dictionary.py         # Dictionary hashing
│   │   └── insert_skills.py           # Skills insertion
│   │
│   ├── services/                      # External services
│   │   ├── cache.py                   # Caching service
│   │   ├── langfuse.py                # Langfuse integration
│   │   └── redis.py                   # Redis client
│   │
│   └── utils/                         # Shared utilities
│       ├── constants.py               # Application constants
│       └── error_code.py              # Error code definitions
│
├── logs/                              # Application logs
├── main.py                            # Application entry point
├── pyproject.toml                     # Poetry configuration
├── poetry.lock                        # Locked dependencies
└── README.md                          # This file
```

### Key Components

- **Routers**: Handle HTTP requests and route them to appropriate handlers
- **Core**: Contains the main business logic organized by feature (job posts, career events)
- **Handlers**: Process requests and coordinate between routers and core logic
- **Helpers**: Utility functions for common tasks like content extraction
- **Services**: External service integrations (Redis, Langfuse)
- **Graph-based Workflows**: Each feature uses LangGraph for complex AI workflows with multiple nodes
