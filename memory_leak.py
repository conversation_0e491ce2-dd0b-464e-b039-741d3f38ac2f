import asyncio
import gc
import time
import tracemalloc
from typing import Dict

import psutil
from memory_profiler import profile

from app.core.job_post import ParseJobPostInput

# Import the parser functions and dependencies
from app.handlers.parse import job_post_parse, job_post_parse_v2


class MemoryProfiler:
    """Memory profiler for analyzing memory leaks in parser functions"""

    def __init__(self):
        self.process = psutil.Process()
        self.initial_memory = None
        self.memory_snapshots = []

    def get_memory_info(self) -> Dict[str, float]:
        """Get current memory information"""
        memory_info = self.process.memory_info()
        return {
            "rss": memory_info.rss / 1024 / 1024,  # MB
            "vms": memory_info.vms / 1024 / 1024,  # MB
            "percent": self.process.memory_percent(),
            "available": psutil.virtual_memory().available / 1024 / 1024,  # MB
        }

    def start_monitoring(self):
        """Start memory monitoring"""
        self.initial_memory = self.get_memory_info()
        self.memory_snapshots = []
        tracemalloc.start()
        print(f"Initial memory usage: {self.initial_memory}")

    def take_snapshot(self, label: str = ""):
        """Take a memory snapshot"""
        current_memory = self.get_memory_info()
        snapshot = {
            "label": label,
            "timestamp": time.time(),
            "memory": current_memory,
            "diff_from_initial": {
                "rss": current_memory["rss"] - self.initial_memory["rss"],
                "vms": current_memory["vms"] - self.initial_memory["vms"],
            },
        }
        self.memory_snapshots.append(snapshot)
        print(
            f"[{label}] Memory: RSS={current_memory['rss']:.2f}MB, "
            f"VMS={current_memory['vms']:.2f}MB, "
            f"Diff RSS={snapshot['diff_from_initial']['rss']:.2f}MB"
        )
        return snapshot

    def get_top_memory_consumers(self, limit: int = 10):
        """Get top memory consuming objects"""
        if not tracemalloc.is_tracing():
            return []

        snapshot = tracemalloc.take_snapshot()
        top_stats = snapshot.statistics("lineno")

        print(f"\nTop {limit} memory consumers:")
        for index, stat in enumerate(top_stats[:limit], 1):
            print(f"{index}. {stat}")

        return top_stats[:limit]

    def force_garbage_collection(self):
        """Force garbage collection and measure impact"""
        before = self.get_memory_info()
        collected = gc.collect()
        after = self.get_memory_info()

        print(f"Garbage collection: collected {collected} objects")
        print(f"Memory before GC: RSS={before['rss']:.2f}MB")
        print(f"Memory after GC: RSS={after['rss']:.2f}MB")
        print(f"Memory freed: {before['rss'] - after['rss']:.2f}MB")

        return {
            "collected_objects": collected,
            "memory_freed": before["rss"] - after["rss"],
            "before": before,
            "after": after,
        }

    def generate_report(self):
        """Generate a comprehensive memory report"""
        print("\n" + "=" * 60)
        print("MEMORY PROFILING REPORT")
        print("=" * 60)

        if not self.memory_snapshots:
            print("No memory snapshots available")
            return

        print(f"Initial memory: RSS={self.initial_memory['rss']:.2f}MB")
        print(f"Final memory: RSS={self.memory_snapshots[-1]['memory']['rss']:.2f}MB")
        print(
            f"Total memory increase: {self.memory_snapshots[-1]['diff_from_initial']['rss']:.2f}MB"
        )

        print("\nMemory timeline:")
        for snapshot in self.memory_snapshots:
            print(
                f"  {snapshot['label']}: RSS={snapshot['memory']['rss']:.2f}MB "
                f"(+{snapshot['diff_from_initial']['rss']:.2f}MB)"
            )

        # Check for potential memory leaks
        if len(self.memory_snapshots) >= 2:
            memory_growth = (
                self.memory_snapshots[-1]["memory"]["rss"]
                - self.memory_snapshots[0]["memory"]["rss"]
            )
            if memory_growth > 10:  # More than 10MB growth
                print(
                    f"\n⚠️  POTENTIAL MEMORY LEAK DETECTED: {memory_growth:.2f}MB growth"
                )
            else:
                print(f"\n✅ Memory usage appears stable: {memory_growth:.2f}MB growth")


async def create_test_data() -> ParseJobPostInput:
    """Create test data for parser functions"""
    return ParseJobPostInput(
        content="**DevOps Engineer**\n\n**Tổng quan công ty:**\nNavigos Group là một trong những công ty hàng đầu ở Việt Nam trong lĩnh vực cung cấp dịch vụ tuyển dụng nhân sự. Chúng tôi cam kết giúp đỡ các cá nhân và doanh nghiệp đạt được ước mơ của mìn thông qua các dịch vụ tuyển dụng chuyên nghiệp, từ tìm kiếm việc làm đến tìm kiếm lãnh đạo cao cấp.\n\n**Tóm tắt công việc:**\nChúng tôi đang tìm kiếm một Kỹ sư DevOps tài năng để gia nhập đội ngũ của mình. Trong vai trò này, bạn sẽ chịu trách nhiệm quản lý vàtối ưu hóa quy trình phát triển phần mềm, cũng như bảo đảm tính khả dụng và bảo mật của hệ thống. Bạn sẽ làm việc chặt chẽ với các nhóm phát triển và quản lý để thúc đẩy những giải pháp sáng tạo và hiệu quả.\n\n**Trách nhiệm công việc:**\n- Thiết kếê, xây dựng và duy trì hạ tầng CI/CD.\n- Quảnnn lýyyyy ngunf và giám sát các hệ thống máy chủ và các ứng dụng trên đám mây.\n- Đảm bảo hiệu suất cao và khả năng mở rộng của các ứng dụng.\n- Phối hợp với nhóm phát triển để tự động hóa quy trình triển khai.\n- Thực hiện các biện pháp bảo mật và khôi phục sau thảm họa.\n- Phân tích và khắc phục các sự cố hệ thống nhanh chóng.\n- Cập nhật thường xyên và triển khai bản vá cho phần mềm và hạ tầng.\n- Đưa ra các giải pháp cải tiến và tối ưu hóa cho quy trình làm việc.\n- Hỗ trợ đào tạo cho các thành viên khác trong nhóm.\n\n**Yêu cầu và kỹ năng:**\n- Kinh nghiệm làm việc trong vai trò DevOps hoặc tương đương.\n- Thành thạo các công cụ quản lý hạ tầng như Terraform, Ansible hoặc Docker.\n- Kiến thức về các dịch vụ đám mây như AWS, Azure hoặc Google Cloud.\n- Kinh nghiệm làm việc với các hệ thống Linux/Unix.\n- Kỹ năng lập trình tốt với một số ngôn ngữ như Python, Bash hoặc Go.\n- Khả năng làm việc nhóm và giao tiếp hiệu quả.\n- Tinh thần cầu tiến và sẵn sàng học hỏi.\n\n**Những kỹ năng ưu tiên:**\n- Có chứng chỉ DevOps hoặc liên quan.\n- Kinh nghiệm làm việc trong Agile/Scrum.\n- Kiến thức về quản lý cơ sở dữ liệu.\n\n**Phúc lợi:**\n- Môi trường làm việc thân thiện, chuyên nghiệp.\n- Cơ hội phát triển nghề nghiệp trong ngành tuyển dụng.\n- Được đào tạo và nâng cao kỹ năng chuyên môn.\n- Chính sách đãi ngộ cạnh tranh.\n- Bảo hiểm y tế đầy đủ và các phúc lợi bổ sung."
    )


@profile
async def profile_job_post_parse(test_data: ParseJobPostInput, iterations: int = 5):
    """Profile the job_post_parse function"""
    results = []
    for i in range(iterations):
        try:
            result = await job_post_parse(test_data)
            results.append(result)
        except Exception as e:
            print(f"Error in iteration {i}: {e}")
    return results


@profile
async def profile_job_post_parse_v2(test_data: ParseJobPostInput, iterations: int = 5):
    """Profile the job_post_parse_v2 function"""
    results = []
    for i in range(iterations):
        try:
            result = await job_post_parse_v2(test_data)
            results.append(result)
        except Exception as e:
            print(f"Error in iteration {i}: {e}")
    return results


async def run_memory_leak_test():
    """Run comprehensive memory leak test"""
    profiler = MemoryProfiler()
    profiler.start_monitoring()

    # Create test data
    test_data = await create_test_data()
    profiler.take_snapshot("After creating test data")

    # Test job_post_parse function
    print("\n" + "=" * 50)
    print("Testing job_post_parse function")
    print("=" * 50)

    for i in range(1):
        profiler.take_snapshot(f"Before job_post_parse iteration {i+1}")
        try:
            await profile_job_post_parse(test_data, iterations=1)
        except Exception as e:
            print(f"Error in job_post_parse: {e}")
        profiler.take_snapshot(f"After job_post_parse iteration {i+1}")

        # Force garbage collection between iterations
        profiler.force_garbage_collection()

    # Test job_post_parse_v2 function
    print("\n" + "=" * 50)
    print("Testing job_post_parse_v2 function")
    print("=" * 50)

    for i in range(1):
        profiler.take_snapshot(f"Before job_post_parse_v2 iteration {i+1}")
        try:
            await profile_job_post_parse_v2(test_data, iterations=1)
        except Exception as e:
            print(f"Error in job_post_parse_v2: {e}")
        profiler.take_snapshot(f"After job_post_parse_v2 iteration {i+1}")

        # Force garbage collection between iterations
        profiler.force_garbage_collection()

    # Get top memory consumers
    profiler.get_top_memory_consumers()

    # Final garbage collection
    profiler.take_snapshot("Before final GC")
    profiler.force_garbage_collection()
    profiler.take_snapshot("After final GC")

    # Generate report
    profiler.generate_report()


if __name__ == "__main__":
    print("Starting memory leak analysis for parser functions...")

    # Run comprehensive memory leak test
    asyncio.run(run_memory_leak_test())

    print("\nMemory leak analysis completed!")
