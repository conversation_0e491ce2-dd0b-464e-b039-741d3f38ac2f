from functools import wraps
from async_lru import alru_cache


def alru_cache_non_empty(*args, **kwargs):
    """Cache decorator that only caches non-empty list results"""

    def decorator(func):
        # Create the cached version
        cached_func = alru_cache(*args, **kwargs)(func)

        @wraps(func)
        async def wrapper(*func_args, **func_kwargs):
            # Call the cached function
            result = await cached_func(*func_args, **func_kwargs)

            # If result is empty list, clear cache entry and call original function
            if isinstance(result, list) and len(result) == 0:
                # Clear this specific cache entry
                cached_func.cache_clear()
                # Call the original function without cache
                return await func(*func_args, **func_kwargs)

            # For non-empty results, return the cached result
            return result

        # Preserve cache methods
        wrapper.cache_info = cached_func.cache_info
        wrapper.cache_clear = cached_func.cache_clear

        return wrapper

    return decorator
