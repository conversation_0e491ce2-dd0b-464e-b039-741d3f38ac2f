from typing import Any, Callable, List, Optional, cast

from langchain_community.tools import DuckDuckGoSearchResults
from langchain_community.utilities import DuckDuckGoSearchAPIWrapper
from langchain_core.runnables import RunnableConfig
from langchain_core.tools import InjectedToolArg
from typing_extensions import Annotated

from app.core.configuration import Configuration


async def search(
    query: str, *, config: Annotated[RunnableConfig, InjectedToolArg]
) -> Optional[list[dict[str, Any]]]:
    """Search for general web results.

    This function performs a search using the DuckDuckGo search engine, which is designed
    to provide comprehensive, accurate, and trusted results. It's particularly useful
    for get company information from company website.
    """
    configuration = Configuration.from_runnable_config(
        config,
    )
    wrapper = DuckDuckGoSearchAPIWrapper(time=None, backend="html", source="text")

    search_ddg = DuckDuckGoSearchResults(
        api_wrapper=wrapper, max_results=configuration.max_search_results
    )

    result = await search_ddg.ainvoke(query)
    return cast(list[dict[str, Any]], result)


TOOLS: List[Callable[..., Any]] = [search]
