from dataclasses import dataclass, field
from typing import List


@dataclass
class ScreeningQuestionsInput:
    job_title: str = ""
    job_description: str = ""
    job_mandatory_requirement: str = ""
    job_should_have_requirement: str = ""
    skills: str = ""
    location: str = ""


@dataclass
class ScreeningQuestionsState(ScreeningQuestionsInput):
    language: str = ""
    screening_questions: List[dict] = field(default_factory=list)


@dataclass
class ScreeningQuestionsOutput:
    screening_questions: List[dict]
