from langgraph.graph import END, START, StateGraph

from app.core.job_post.screening_questions.nodes import (
    detect_language,
    generate_screening_questions,
    translate,
)
from app.core.job_post.screening_questions.state import (
    ScreeningQuestionsInput,
    ScreeningQuestionsOutput,
    ScreeningQuestionsState,
)

# Define a new graph
workflow = StateGraph(
    ScreeningQuestionsState,
    input=ScreeningQuestionsInput,
    output=ScreeningQuestionsOutput,
)


def should_translate(state: ScreeningQuestionsState):
    if state.language not in ["en", "vi"]:
        return "translate"
    return "generate_screening_questions"


workflow.add_node("detect_language", detect_language)
workflow.add_node("translate", translate)
workflow.add_node("generate_screening_questions", generate_screening_questions)

workflow.add_edge(START, "detect_language")
workflow.add_conditional_edges(
    "detect_language", should_translate, ["translate", "generate_screening_questions"]
)
workflow.add_edge("translate", "generate_screening_questions")
workflow.add_edge("generate_screening_questions", END)

graph = workflow.compile()
graph.name = "Generate Screening Questions"
