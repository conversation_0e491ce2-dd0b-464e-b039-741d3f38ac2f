from dotenv import load_dotenv
from langchain_openai import ChatOpenAI
from pydantic import BaseModel
from trustcall import create_extractor

from app.core.job_post.screening_questions.state import ScreeningQuestionsState

load_dotenv()


class TranslateVietnamese(BaseModel):
    job_title: str = ""
    job_description: str = ""
    job_mandatory_requirement: str = ""
    job_should_have_requirement: str = ""
    skills: str = ""
    location: str = ""


async def translate(state: ScreeningQuestionsState):
    user_prompt = (
        f"Job title: {state.job_title}\n"
        f"Job description: \n{state.job_description}\n"
        f"Job mandatory requirement: \n{state.job_mandatory_requirement}\n"
        f"Job should have requirement: \n{state.job_should_have_requirement}\n"
        f"Skills: {state.skills}\n"
        f"Location: {state.location}"
    )
    messages = [
        {"role": "system", "content": "Translate to Vietnamese."},
        {
            "role": "user",
            "content": user_prompt,
        },
    ]
    llm = ChatOpenAI(model="gpt-4o-mini", temperature=0)
    bound = create_extractor(
        llm=llm, tools=[TranslateVietnamese], tool_choice="TranslateVietnamese"
    )
    response = await bound.ainvoke(messages)
    return {**response["responses"][0].model_dump()}
