from langid.langid import LanguageIdentifier, model

from app.core.job_post.screening_questions.state import ScreeningQuestionsState

identifier = LanguageIdentifier.from_modelstring(model, norm_probs=True)


async def detect_language(state: ScreeningQuestionsState):
    user_prompt = (
        f"Job title: {state.job_title}\n"
        f"Job description: \n{state.job_description}\n"
        f"Job mandatory requirement: \n{state.job_mandatory_requirement}\n"
        f"Job should have requirement: \n{state.job_should_have_requirement}\n"
        f"Skills: {state.skills}\n"
        f"Location: {state.location}"
    )
    lang = identifier.classify(user_prompt)[0]
    return {"language": lang}
