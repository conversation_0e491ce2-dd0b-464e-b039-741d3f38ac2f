from typing import List, Literal

from dotenv import load_dotenv
from langchain_openai import ChatOpenAI
from pydantic import BaseModel, Field
from trustcall import create_extractor

from app.core.job_post.screening_questions.prompt import (
    GENERATE_SCREENING_QUESTIONS_PROMPT,
)
from app.core.job_post.screening_questions.state import ScreeningQuestionsState

load_dotenv()


class ScreeningQuestion(BaseModel):
    question: str = Field(..., description="The screening question")
    answers: List[str] = Field(
        ..., description="List answers of the screening question"
    )
    question_type: Literal["single_response", "multiple_response"] = Field(
        ..., description="The question type of the screening question"
    )


class ScreeningQuestions(BaseModel):
    screening_questions: List[ScreeningQuestion] = Field(
        ..., description="List of the screening questions"
    )


async def generate_screening_questions(state: ScreeningQuestionsState):
    user_prompt = (
        f"Job title: {state.job_title}\n"
        f"Job description: \n{state.job_description}\n"
        f"Job mandatory requirement: \n{state.job_mandatory_requirement}\n"
        f"Job should have requirement: \n{state.job_should_have_requirement}\n"
        f"Skills: {state.skills}\n"
        f"Location: {state.location}"
    )
    messages = [
        {"role": "system", "content": GENERATE_SCREENING_QUESTIONS_PROMPT},
        {
            "role": "user",
            "content": user_prompt,
        },
    ]
    llm = ChatOpenAI(model="gpt-4o-mini", temperature=1)
    bound = create_extractor(
        llm=llm, tools=[ScreeningQuestions], tool_choice="ScreeningQuestions"
    )
    response = await bound.ainvoke(messages)
    return {
        "screening_questions": response["responses"][0].model_dump()[
            "screening_questions"
        ]
    }
