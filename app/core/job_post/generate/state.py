from dataclasses import dataclass, field
from typing import List, Literal


@dataclass
class GenerateJobPostInput:
    job_title: str = ""
    job_details: str = ""
    language: Literal["Vietnamese", "English"] = "Vietnamese"
    company_information: str = ""


@dataclass
class GenerateJobPostState(GenerateJobPostInput):
    is_valid_job_title: bool = True
    is_suggested: bool = False
    job_title_suggested: str = ""
    job_post_sections: List[dict] = field(default_factory=list)


@dataclass
class GenerateJobPostOutput:
    is_suggested: bool
    job_title_suggested: str
    job_post_sections: List[dict]
