from enum import Enum
from typing import List

from pydantic import BaseModel, Field


class SectionTitleEn(str, Enum):
    job_summary = "Job Summary"
    job_responsibilities = "Job Responsibilities"
    qualifications_skills = "Qualifications & Skills"
    preferred_qualifications = "Preferred Qualifications"
    benefits = "Benefits"


class SectionTitleVi(str, Enum):
    job_summary = "Tóm tắt công việc"
    job_responsibilities = "Mô tả công việc"
    qualifications_skills = "Yêu cầu & <PERSON>ỹ năng"
    preferred_qualifications = "Yêu cầu ưu tiên"
    benefits = "Phúc lợi"


class SectionVi(BaseModel):
    section_title: SectionTitleVi
    description: str = Field(
        description="Content of the section. Can be either a single string paragraph or a list of bullet points."
    )


class SectionEn(BaseModel):
    section_title: SectionTitleEn
    description: str = Field(
        description="Content of the section. Can be either a single string paragraph or a list of bullet points."
    )


class JobPostEn(BaseModel):
    sections: List[SectionEn] = Field(
        description="List of sections that make up the job posting in English. Each section has a title and description."
    )


class JobPostVi(BaseModel):
    sections: List[SectionVi] = Field(
        description="List of sections that make up the job posting in Vietnamese. Each section has a title and description."
    )
