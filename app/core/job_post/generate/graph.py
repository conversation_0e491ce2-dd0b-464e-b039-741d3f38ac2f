from langgraph.graph import END, START, StateGraph

from app.core.job_post.generate.nodes import (
    generate_job_post,
    suggest_job_title,
    validate_job_title,
)
from app.core.job_post.generate.state import (
    GenerateJobPostInput,
    GenerateJobPostOutput,
    GenerateJobPostState,
)

# Define a new graph
workflow = StateGraph(
    GenerateJobPostState, input=GenerateJobPostInput, output=GenerateJobPostOutput
)


def should_continue(state: GenerateJobPostState):
    if not state.is_valid_job_title:
        return END
    return "suggest_job_title"


workflow.add_node("validate_job_title", validate_job_title)
workflow.add_node("suggest_job_title", suggest_job_title)
workflow.add_node("generate_job_post", generate_job_post)

workflow.add_edge(START, "validate_job_title")
workflow.add_conditional_edges(
    "validate_job_title", should_continue, ["suggest_job_title", END]
)
workflow.add_edge("suggest_job_title", "generate_job_post")
workflow.add_edge("generate_job_post", END)

graph = workflow.compile()
graph.name = "Job Post Generate"
