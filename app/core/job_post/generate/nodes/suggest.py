from langchain_openai import ChatOpenAI
from pydantic import BaseModel, <PERSON>
from trustcall import create_extractor

from app.core.job_post.generate.prompt import SUGGEST_JOB_TITLE_PROMPT
from app.core.job_post.generate.state import GenerateJobPostState


class SuggestJobTitle(BaseModel):
    original_job_title: str = Field(description="Original job title")
    job_title_suggested: str = Field(description="Suggest job title")


llm = ChatOpenAI(model="gpt-4o-mini", temperature=0.5)


async def suggest_job_title(state: GenerateJobPostState):
    messages = [
        {"role": "system", "content": SUGGEST_JOB_TITLE_PROMPT},
        {
            "role": "user",
            "content": state.job_title,
        },
    ]
    bound = create_extractor(
        llm=llm, tools=[SuggestJobTitle], tool_choice="SuggestJobTitle"
    )
    response = await bound.ainvoke(messages)
    if state.job_title != response["responses"][0].job_title_suggested:
        return {
            "is_suggested": True,
            "job_title_suggested": response["responses"][0].job_title_suggested,
        }

    return {
        "is_suggested": False,
        "job_title_suggested": response["responses"][0].job_title_suggested,
    }
