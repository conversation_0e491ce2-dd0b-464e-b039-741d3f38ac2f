from dotenv import load_dotenv
from langchain_openai import ChatOpenAI
from trustcall import create_extractor

from app.core.job_post.generate.job_post_model import JobPostEn, JobPostVi
from app.core.job_post.generate.prompt import GENERATE_JOB_POST_PROMPT
from app.core.job_post.generate.state import GenerateJobPostState

load_dotenv()

llm = ChatOpenAI(model="gpt-4o-mini", temperature=1.0)


async def generate_job_post(state: GenerateJobPostState):
    user_prompt = (
        "## User input\n"
        f"Job title: {state.job_title_suggested}\n"
        f"Job details: {state.job_details}\n"
        f"Company information: {state.company_information}\n"
        "## Notes\n"
        f"The sections title and the job posting must be written in {state.language}."
    )
    messages = [
        {"role": "system", "content": GENERATE_JOB_POST_PROMPT},
        {"role": "user", "content": user_prompt},
    ]
    if state.language == "English":
        tools = [JobPostEn]
        tool_choice = "JobPostEn"
    else:
        tools = [JobPostVi]
        tool_choice = "JobPostVi"
    bound = create_extractor(llm=llm, tools=tools, tool_choice=tool_choice)
    response = await bound.ainvoke(messages)
    job_post_sections = []
    for section in response["responses"][0].model_dump()["sections"]:
        section_title = section.get("section_title")
        description = section.get("description")
        section = {"section_title": section_title, "description": description}
        job_post_sections.append(section)
    return {"job_post_sections": job_post_sections}
