from dotenv import load_dotenv
from langchain_openai import ChatOpenAI
from trustcall import create_extractor

from app.core.job_post.generate.prompt import VALIDATE_JOB_TITLE_PROMPT
from app.core.job_post.generate.state import GenerateJobPostState
from app.core.job_post.generate.validate_job_title import ValidateJobTitle

load_dotenv()

llm = ChatOpenAI(model="gpt-4o-mini", temperature=0)


async def validate_job_title(state: GenerateJobPostState):
    messages = [
        {"role": "system", "content": VALIDATE_JOB_TITLE_PROMPT},
        {
            "role": "user",
            "content": state.job_title,
        },
    ]
    bound = create_extractor(
        llm=llm, tools=[ValidateJobTitle], tool_choice="ValidateJobTitle"
    )
    response = await bound.ainvoke(messages)
    return {"is_valid_job_title": response["responses"][0].is_validity}
