from enum import Enum
from typing import Optional

import httpx
from async_lru import alru_cache
from langchain_openai import ChatOpenAI
from pydantic import create_model

from app.core.utils import _get_or_create_enum
from app.helpers import get_district


@alru_cache(maxsize=100)
async def get_currency_exchange_rate(src: str, dst: str):
    async with httpx.AsyncClient(timeout=httpx.Timeout(timeout=5)) as client:
        response = await client.get(f"https://open.er-api.com/v6/latest/{src.upper()}")
        if response.status_code == 200:
            if response.json().get("result") == "success":
                return response.json().get("rates", {}).get(dst.upper())
            else:
                return None
        else:
            return None


def postprocess_benefits(benefits, job_benefit_list):
    job_benefit_id_enum = {
        job_benefit["nameEn"]: job_benefit["id"] for job_benefit in job_benefit_list
    }
    BenefitIdEnum = _get_or_create_enum("BenefitIdEnum", job_benefit_id_enum)

    benefit_groups = {}
    for benefit in benefits:
        benefit_type = benefit.get("benefit_type")
        benefit_description = benefit.get("benefit_description")
        if benefit_type in benefit_groups:
            benefit_groups[benefit_type].append(benefit_description)
        else:
            benefit_groups[benefit_type] = [benefit_description]

    new_benefits = []
    for benefit_type, benefit_descriptions in benefit_groups.items():
        concatenated = ". ".join(
            benefit_description.rstrip(".")
            for benefit_description in benefit_descriptions
        )
        new_benefits.append(
            {
                "benefit_id": BenefitIdEnum[benefit_type].value,
                "benefit_type": benefit_type,
                "benefit_description": concatenated[:500],
            }
        )
    return new_benefits


async def postprocess_working_location(job_extra_info, city):
    working_location = (
        job_extra_info.get("working_location")
        if job_extra_info.get("working_location")
        else []
    )
    new_working_location = []

    city_id_enum = {data["nameEn"]: data["id"] for data in city}
    CityIdEnum = _get_or_create_enum("CityIdEnum", city_id_enum)
    for data in working_location:
        city_name = data.get("city_name")
        address = data.get("full_address")
        if city_name and city_name != "Other":
            city_id = CityIdEnum[city_name].value
            district_list = await get_district(city_id)
            if district_list:
                district_enum = {
                    district["nameEn"]: district["nameEn"] for district in district_list
                }
                DistrictEnum = Enum("DistrictEnum", district_enum)

                district_id_enum = {
                    district["nameEn"]: district["id"] for district in district_list
                }
                DistrictIdEnum = Enum("DistrictIdEnum", district_id_enum)

                District = create_model(
                    "District", district=(Optional[DistrictEnum], None)
                )
                llm = ChatOpenAI(model="gpt-4o-mini", temperature=0)
                try:
                    chain = llm.with_structured_output(District)
                    response = await chain.ainvoke(address)
                    district = response.district
                except Exception:
                    district = None
                if district:
                    new_working_location.append(
                        {
                            "address": address,
                            "city_id": city_id,
                            "city": city_name,
                            "district_id": DistrictIdEnum[district.value].value,
                            "district": district.value,
                        }
                    )
                else:
                    new_working_location.append(
                        {
                            "address": address,
                            "city_id": city_id,
                            "city": city_name,
                            "district_id": None,
                            "district": None,
                        }
                    )
        else:
            new_working_location.append(
                {
                    "address": address,
                    "city_id": None,
                    "city": None,
                    "district_id": None,
                    "district": None,
                }
            )
    return new_working_location


async def postprocess_job_extra_info(job_extra_info, metadata):
    salary_payment_period_list = metadata["salaryPaymentPeriod"]
    salary_payment_period_id_enum = {
        salary_payment_period["nameEn"]: salary_payment_period["id"]
        for salary_payment_period in salary_payment_period_list
    }
    SalaryPaymentPeriodIdEnum = _get_or_create_enum(
        "SalaryPaymentPeriodIdEnum", salary_payment_period_id_enum
    )
    salary_payment_period = (
        job_extra_info.get("salary_payment_period")
        if job_extra_info.get("salary_payment_period")
        else None
    )
    if salary_payment_period:
        salary_payment_period_value = job_extra_info["salary_payment_period"]
        job_extra_info["salary_payment_period"] = {
            "salary_payment_period": salary_payment_period_value,
            "salary_payment_period_id": SalaryPaymentPeriodIdEnum[
                salary_payment_period_value
            ].value,
        }

    salary_currency_list = metadata["salaryCurrency"]
    salary_currency_id_enum = {
        salary_currency["nameEn"]: salary_currency["id"]
        for salary_currency in salary_currency_list
    }
    SalaryCurrencyIdEnum = _get_or_create_enum(
        "SalaryCurrencyIdEnum", salary_currency_id_enum
    )
    salary_currency = (
        job_extra_info.get("salary_currency")
        if job_extra_info.get("salary_currency")
        else None
    )
    if salary_currency:
        salary_currency_value = job_extra_info["salary_currency"].title()
        if salary_currency_value not in ["Usd", "Vnd"]:
            salary_amount = (
                job_extra_info.get("salary_amount")
                if job_extra_info.get("salary_amount")
                else None
            )

            if salary_amount:
                exchange_rate = await get_currency_exchange_rate(
                    src="VND", dst=salary_currency_value.upper()
                )
                if exchange_rate:
                    salary_min = (
                        salary_amount.get("salary_min")
                        if salary_amount.get("salary_min")
                        else None
                    )
                    if salary_min:
                        exchanged_salary_min = salary_min // exchange_rate
                        job_extra_info["salary_amount"]["salary_min"] = (
                            exchanged_salary_min
                        )
                    salary_max = (
                        salary_amount.get("salary_max")
                        if salary_amount.get("salary_max")
                        else None
                    )
                    if salary_max:
                        exchanged_salary_max = salary_max // exchange_rate
                        job_extra_info["salary_amount"]["salary_max"] = (
                            exchanged_salary_max
                        )
                else:
                    job_extra_info["salary_amount"]["salary_min"] = 0.0
                    job_extra_info["salary_amount"]["salary_max"] = 0.0

            salary_currency_value = "Vnd"
        job_extra_info["salary_currency"] = {
            "salary_currency": salary_currency_value,
            "salary_currency_id": SalaryCurrencyIdEnum[salary_currency_value].value,
        }

    job_experience_year_list = metadata["experience"]
    job_experience_year_id_enum = {
        job_experience_year["nameEn"]: job_experience_year["id"]
        for job_experience_year in job_experience_year_list
    }
    JobExperienceYearIdEnum = _get_or_create_enum(
        "JobExperienceYearIdEnum", job_experience_year_id_enum
    )
    job_experience_year = (
        job_extra_info.get("job_experience_year")
        if job_extra_info.get("job_experience_year")
        else None
    )
    if job_experience_year:
        job_experience_year_value = job_extra_info["job_experience_year"]
        job_extra_info["job_experience_year"] = {
            "job_experience_year": job_experience_year_value,
            "job_experience_year_id": JobExperienceYearIdEnum[
                job_experience_year_value
            ].value,
        }

    working_arrangement_list = metadata["locationType"]
    working_arrangement_id_enum = {
        working_arrangement["nameEn"]: working_arrangement["id"]
        for working_arrangement in working_arrangement_list
    }
    WorkingArrangmentIdEnum = _get_or_create_enum(
        "WorkingArrangmentIdEnum", working_arrangement_id_enum
    )
    working_arrangement = (
        job_extra_info.get("working_arrangement")
        if job_extra_info.get("working_arrangement")
        else None
    )
    if working_arrangement:
        working_arrangement_value = job_extra_info["working_arrangement"]
        job_extra_info["working_arrangement"] = {
            "working_arrangement": working_arrangement_value,
            "working_arrangement_id": WorkingArrangmentIdEnum[
                working_arrangement_value
            ].value,
        }

    employment_type_list = metadata["employmentType"]
    employment_type_id_enum = {
        employment_type["nameEn"]: employment_type["id"]
        for employment_type in employment_type_list
    }
    EmploymentTypeIdEnum = _get_or_create_enum(
        "EmploymentTypeIdEnum", employment_type_id_enum
    )
    employment_type = (
        job_extra_info.get("employment_type")
        if job_extra_info.get("employment_type")
        else None
    )
    if employment_type:
        employment_type_value = job_extra_info["employment_type"]
        job_extra_info["employment_type"] = {
            "employment_type": employment_type_value,
            "employment_type_id": EmploymentTypeIdEnum[employment_type_value].value,
        }

    job_level_list = metadata["jobLevels"]
    job_level_id_enum = {
        job_level["nameEn"]: job_level["id"] for job_level in job_level_list
    }
    JobLevelIdEnum = _get_or_create_enum("JobLevelIdEnum", job_level_id_enum)
    job_level = (
        job_extra_info.get("job_level") if job_extra_info.get("job_level") else None
    )
    if job_level:
        job_level_value = job_extra_info["job_level"]
        job_extra_info["job_level"] = {
            "job_level": job_level_value,
            "job_level_id": JobLevelIdEnum[job_level_value].value,
        }

    education_level_list = metadata["educations"]
    education_level_id_enum = {
        education_level["nameEn"]: education_level["id"]
        for education_level in education_level_list
    }
    EducationLevelIdEnum = _get_or_create_enum(
        "EducationLevelIdEnum", education_level_id_enum
    )
    education_level = (
        job_extra_info.get("education_level")
        if job_extra_info.get("education_level")
        else None
    )
    if education_level:
        education_level_value = job_extra_info["education_level"]
        job_extra_info["education_level"] = {
            "education_level": education_level_value,
            "education_level_id": EducationLevelIdEnum[education_level_value].value,
        }

    working_day_list = metadata["workingDay"]
    working_day_id_enum = {
        working_day["nameEn"]: working_day["id"] for working_day in working_day_list
    }
    WorkingDayIdEnum = _get_or_create_enum("WorkingDayIdEnum", working_day_id_enum)
    working_day = (
        job_extra_info.get("working_day") if job_extra_info.get("working_day") else []
    )
    new_working_day = []
    for data in working_day:
        new_working_day.append(
            {
                "working_day": data,
                "working_day_id": WorkingDayIdEnum[data].value,
            }
        )
    job_extra_info.update({"working_day": new_working_day if new_working_day else None})
    return job_extra_info
