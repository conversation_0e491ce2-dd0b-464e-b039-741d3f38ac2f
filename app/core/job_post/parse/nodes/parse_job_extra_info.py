from langchain_openai import ChatOpenAI
from trustcall import create_extractor

from app.core.job_post.parse.model import generate_model_job_extra_info
from app.core.job_post.parse.state import ParseJobPostState
from app.core.job_post.parse.utils import (
    postprocess_job_extra_info,
    postprocess_working_location,
)

llm = ChatOpenAI(model="gpt-4o-mini", temperature=0)


async def parse_job_extra_info(state: ParseJobPostState):
    job_post_content = state.content
    JobExtraInfo = await generate_model_job_extra_info(state.metadata)
    bound = create_extractor(
        llm,
        tools=[JobExtraInfo],
        tool_choice="JobExtraInfo",
    )
    response = await bound.ainvoke(
        f"""Extract the job information from the following job post content (Avoid hallucination):
<job_post_content>
{job_post_content}
</job_post_content>"""
    )
    job_extra_info = response["responses"][0].model_dump(mode="json")
    new_working_location = await postprocess_working_location(
        job_extra_info, state.metadata.get("city", [])
    )
    job_extra_info.update(
        {"working_location": new_working_location if new_working_location else None}
    )
    new_job_extra_info = await postprocess_job_extra_info(
        job_extra_info, state.metadata
    )
    return {"job_extra_info": new_job_extra_info}
