from langchain_openai import ChatOpenAI
from pydantic import BaseModel
from trustcall import create_extractor

from app.core.job_post.parse.prompt import VALIDATE_CONTENT_PROMPT
from app.core.job_post.parse.state import ParseJobPostState


class ValidateJobPost(BaseModel):
    is_validity: bool
    reasoning: str


llm = ChatOpenAI(model="gpt-4o-mini", temperature=0)
bound = create_extractor(
    llm=llm, tools=[ValidateJobPost], tool_choice="ValidateJobPost"
)


async def validate_content(state: ParseJobPostState):
    content = state.content
    messages = [
        {"role": "system", "content": VALIDATE_CONTENT_PROMPT},
        {
            "role": "user",
            "content": content,
        },
    ]
    response = await bound.ainvoke(messages)
    return {"is_valid_content": response["responses"][0].is_validity}
