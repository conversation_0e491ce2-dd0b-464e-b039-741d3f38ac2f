from app.core.job_post.parse.state import ParseJobPostState
from app.core.job_post.suggest_content.graph import graph as suggest_content_graph


async def suggest_content(state: ParseJobPostState):
    job_info = state.job_info
    response = await suggest_content_graph.ainvoke(
        {
            "job_title": job_info.get("job_title"),
            "job_description": job_info.get("job_description"),
            "job_mandatory_requirement": job_info.get("job_mandatory_requirement"),
            "job_should_have_requirement": job_info.get("job_should_have_requirement")
            if job_info.get("job_should_have_requirement")
            else "",
        }
    )

    return {"job_post_suggest": response["job_post_suggest"]}
