from enum import Enum
from typing import List, Optional

from pydantic import Field, create_model


async def generate_model_job_extra_info(metadata: dict):
    # Create salary amount model SalaryAmount
    SalaryAmount = create_model(
        "SalaryAmount",
        salary_min=(
            float,
            Field(
                ...,
                description="The minimum salary mentioned. If no values are provided, it defaults to 0",
            ),
        ),
        salary_max=(
            float,
            Field(
                ...,
                description="The maximum salary mentioned. If no values are provided, it defaults to 0",
            ),
        ),
    )

    # Create working location model
    city_enum = {data["nameEn"]: data["nameEn"] for data in metadata.get("city", [])}
    CityEnum = Enum("CityEnum", city_enum)

    WorkingLocation = create_model(
        "WorkingLocation",
        full_address=(
            str,
            Field(
                ...,
                description="Complete address of the working location.",
            ),
        ),
        city_name=(Optional[CityEnum], Field(..., description="Name of the city.")),
    )

    # Create salary payment period model SalaryPaymentPeriod
    salary_payment_period_list = metadata.get("salaryPaymentPeriod", [])
    salary_payment_period_enum = {
        salary_payment_period["nameEn"]: salary_payment_period["nameEn"]
        for salary_payment_period in salary_payment_period_list
    }
    SalaryPaymentPeriodEnum = Enum(
        "SalaryPaymentPeriodEnum", salary_payment_period_enum
    )

    # Create salary currency model SalaryCurrent
    # salary_currency_list = metadata.get("salaryCurrency", [])
    # salary_currency_enum = {
    #     salary_currency["nameEn"]: salary_currency["nameEn"]
    #     for salary_currency in salary_currency_list
    # }
    # SalaryCurrentEnum = Enum("SalaryCurrentEnum", salary_currency_enum)

    # Create job experience year model JobExperienceYear
    job_experience_year_list = metadata.get("experience", [])
    job_experience_year_enum = {
        job_experience_year["nameEn"]: job_experience_year["nameEn"]
        for job_experience_year in job_experience_year_list
    }
    JobExperienceYearEnum = Enum("JobExperienceYearEnum", job_experience_year_enum)

    # Create working arrangment model WorkingArrangment
    working_arrangement_list = metadata.get("locationType", [])
    working_arrangement_enum = {
        working_arrangement["nameEn"]: working_arrangement["nameEn"]
        for working_arrangement in working_arrangement_list
    }
    WorkingArrangmentEnum = Enum("WorkingArrangmentEnum", working_arrangement_enum)

    # Create employment type model EmploymentType
    employment_type_list = metadata.get("employmentType", [])
    employment_type_enum = {
        employment_type["nameEn"]: employment_type["nameEn"]
        for employment_type in employment_type_list
    }
    EmploymentTypeEnum = Enum("EmploymentTypeEnum", employment_type_enum)

    # Create job level model JobLevel
    job_level_list = metadata.get("jobLevels", [])
    job_level_enum = {
        job_level["nameEn"]: job_level["nameEn"] for job_level in job_level_list
    }
    JobLevelEnum = Enum("JobLevelEnum", job_level_enum)

    # Create education level model EducationLevel
    education_level_list = metadata.get("educations", [])
    education_level_enum = {
        education_level["nameEn"]: education_level["nameEn"]
        for education_level in education_level_list
    }
    EducationLevelEnum = Enum("EducationLevelEnum", education_level_enum)

    # Create working day model WorkingDay
    working_day_list = metadata.get("workingDay", [])
    working_day_enum = {
        working_day["nameEn"]: working_day["nameEn"] for working_day in working_day_list
    }
    WorkingDayEnum = Enum("WorkingDayEnum", working_day_enum)

    # Create working hour model WorkingHour
    WorkingHour = create_model(
        "WorkingHour",
        start_hour=(str, Field(..., description="Format hour: hh:mm")),
        end_hour=(str, Field(..., description="Format hour: hh:mm")),
    )

    # Create job extra info model
    JobExtraInfo = create_model(
        "JobExtraInfo",
        salary_amount=(
            Optional[SalaryAmount],
            Field(
                None,
                description="The minimum and maximum salary values for the position. Return None if salary does not mentioned",
            ),
        ),
        salary_payment_period=(
            Optional[SalaryPaymentPeriodEnum],
            Field(None, description="Return None if not mentioned"),
        ),
        salary_currency=(
            Optional[str],
            Field(..., description="The salary currency (e.g: USD, VND)"),
        ),
        job_experience_year=(
            Optional[JobExperienceYearEnum],
            Field(None, description="Return None if not mentioned"),
        ),
        working_location=(
            Optional[List[WorkingLocation]],
            Field(
                None,
                description="The physical location or address of the workplace in Vietnam. Returns None if not specified or if location is outside Vietnam.",
            ),
        ),
        working_arrangement=(
            Optional[WorkingArrangmentEnum],
            Field(None, description="Return None if not mentioned"),
        ),
        employment_type=(
            Optional[EmploymentTypeEnum],
            Field(None, description="Return None if not mentioned"),
        ),
        job_level=(
            Optional[JobLevelEnum],
            Field(None, description="Return None if not mentioned"),
        ),
        education_level=(
            Optional[EducationLevelEnum],
            Field(None, description="Return None if does not mentioned"),
        ),
        working_day=(
            Optional[List[WorkingDayEnum]],
            Field(
                None,
                description="The specific working days in the work schedule. Returns None if working day does not specified.",
            ),
        ),
        working_hour=(
            Optional[WorkingHour],
            Field(
                None,
                description="Return None if working hour does not mentioned",
            ),
        ),
        paid_job=(
            bool,
            Field(
                True,
                description="Indicates whether the position is paid (True) or unpaid (False, typically for internships or volunteer work). Defaults to True.",
            ),
        ),
    )

    return JobExtraInfo


async def generate_model_job_info(metadata: dict):
    # Create job benefit model Benefit
    job_benefit_list = metadata.get("benefits", [])
    job_benefit_enum = {
        job_benefit["nameEn"]: job_benefit["nameEn"] for job_benefit in job_benefit_list
    }
    BenefitEnum = Enum("BenefitEnum", job_benefit_enum)
    Benefit = create_model(
        "Benefit",
        benefit_description=(
            str,
            Field(
                ...,
                description="The specific description of the benefit as mentioned in the job post",
            ),
        ),
        benefit_type=(
            BenefitEnum,
            Field(
                ...,
                description="The categorized type of the benefit. If the benefit doesn't match any predefined types exactly, use 'OTHER'",
            ),
        ),
    )

    # Create job info model
    JobInfo = create_model(
        "JobInfo",
        job_title=(
            str,
            Field(
                ...,
                description="The specific title or position name as stated in the job posting",
            ),
        ),
        job_mandatory_requirement=(
            str,
            Field(
                ...,
                description="List of essential qualifications, skills, and requirements that are explicitly stated as mandatory or required for the position. Should keep or use bullet points, ordered list, etc... format and include both English and Vietnamese content if present",
            ),
        ),
        job_should_have_requirement=(
            Optional[str],
            Field(
                None,
                description="List of preferred or 'nice-to-have' qualifications and skills that are not mandatory. Should be None if no preferred requirements are explicitly mentioned.",
            ),
        ),
        job_description=(
            str,
            Field(
                ...,
                description="The complete job description section from the posting, excluding requirements and qualifications. Should use bullet points, ordered list, etc... format and include both English and Vietnamese content if present",
            ),
        ),
        job_benefit=(
            Optional[List[Benefit]],
            Field(
                None,
                description="List of benefits, perks, and compensation packages offered with the position. Each benefit should be categorized according to predefined types, using 'OTHER' for non-standard benefits. Should be None if no benefits are mentioned",
            ),
        ),
    )

    return JobInfo
