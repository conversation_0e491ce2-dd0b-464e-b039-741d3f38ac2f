from dataclasses import dataclass, field
from typing import List


@dataclass
class ParseJobPostInput:
    metadata: dict = field(default_factory=dict)
    content: str = ""


@dataclass
class ParseJobPostState(ParseJobPostInput):
    is_valid_content: bool = True
    job_info: dict = field(default_factory=dict)
    job_extra_info: dict = field(default_factory=dict)
    job_post_parsed: dict = field(default_factory=dict)
    job_post_suggest: dict = field(default_factory=dict)
    skills_required: List[str] = field(default_factory=list)
    most_related_job_function: dict = field(default_factory=dict)
    related_job_function: List[dict] = field(default_factory=list)


@dataclass
class ParseJobPostOutput:
    job_post_parsed: dict
    job_post_suggest: dict
