from langgraph.graph import END, START, StateGraph
from langgraph.pregel import RetryPolicy

from app.core.job_post.parse.nodes import (
    parse_job_extra_info,
    parse_job_info,
    postprocess,
    suggest_content,
    suggest_job_function,
    suggest_skills,
    validate_content,
)
from app.core.job_post.parse.state import (
    ParseJobPostInput,
    ParseJobPostOutput,
    ParseJobPostState,
)

workflow = StateGraph(
    ParseJobPostState, input=ParseJobPostInput, output=ParseJobPostOutput
)


def should_continue(state: ParseJobPostState):
    if not state.is_valid_content:
        return END
    return ["parse_job_info", "parse_job_extra_info"]


retry = RetryPolicy(max_attempts=3)

workflow.add_node("validate_content", validate_content)
workflow.add_node("parse_job_info", parse_job_info, retry=retry)
workflow.add_node("parse_job_extra_info", parse_job_extra_info, retry=retry)
workflow.add_node("suggest_content", suggest_content)
workflow.add_node("suggest_skills", suggest_skills)
workflow.add_node("suggest_job_function", suggest_job_function)
workflow.add_node("postprocess", postprocess)

workflow.add_edge(START, "validate_content")
workflow.add_conditional_edges("validate_content", should_continue)
workflow.add_edge("parse_job_info", "suggest_content")
workflow.add_edge("parse_job_info", "suggest_skills")
workflow.add_edge("parse_job_info", "suggest_job_function")
workflow.add_edge(
    [
        "parse_job_extra_info",
        "suggest_content",
        "suggest_skills",
        "suggest_job_function",
    ],
    "postprocess",
)
workflow.add_edge("postprocess", END)

graph = workflow.compile()
graph.name = "Job Post Parse"
