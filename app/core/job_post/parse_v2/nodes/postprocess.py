from app.core.job_post.parse.state import ParseJobPostState


async def postprocess(state: ParseJobPostState):
    job_post_parsed = {
        **state.job_info,
        **state.job_extra_info,
        "skills_required": state.skills_required,
        "most_related_job_function": state.most_related_job_function,
        "related_job_function": state.related_job_function,
    }
    return {
        "job_post_parsed": job_post_parsed,
    }
