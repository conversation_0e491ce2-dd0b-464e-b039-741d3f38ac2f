from dotenv import load_dotenv
from langchain_openai import ChatOpenAI

from app.core.job_post.parse.model import generate_model_job_info
from app.core.job_post.parse.state import ParseJobPostState
from app.core.job_post.parse.utils import postprocess_benefits

load_dotenv()

llm = ChatOpenAI(model="gpt-4o-mini", temperature=0)


async def parse_job_info(state: ParseJobPostState):
    job_post_content = state.content
    JobInfo = await generate_model_job_info(state.metadata)
    chain = llm.with_structured_output(JobInfo)
    response = await chain.ainvoke(
        f"""Extract the job information from the following job post content (Avoid hallucination):
<job_post_content>
{job_post_content}
</job_post_content>"""
    )
    job_info = response.model_dump(mode="json")
    benefits = job_info.get("job_benefit") if job_info.get("job_benefit") else []
    new_benefits = postprocess_benefits(benefits, state.metadata.get("benefits", []))

    # Sorted key order in response
    new_job_info = {
        "job_title": job_info.get("job_title", ""),
        "job_description": job_info.get("job_description", "").replace("**", ""),
        "job_mandatory_requirement": job_info.get("job_mandatory_requirement").replace(
            "**", ""
        )
        if job_info.get("job_mandatory_requirement")
        else None,
        "job_should_have_requirement": job_info.get(
            "job_should_have_requirement"
        ).replace("**", "")
        if job_info.get("job_should_have_requirement")
        else None,
    }
    new_job_info.update({"job_benefit": new_benefits if new_benefits else None})
    return {"job_info": new_job_info}
