from app.core.job_post.parse.state import ParseJobPostState
from app.core.job_post.suggest_skills.graph import graph as suggest_skills_graph
from app.helpers import insert_skills


async def suggest_skills(state: ParseJobPostState):
    job_info = state.job_info
    response = await suggest_skills_graph.ainvoke(
        {
            "job_title": job_info.get("job_title", ""),
            "job_description": job_info.get("job_description", ""),
            "job_mandatory_requirement": job_info.get("job_mandatory_requirement"),
            "job_should_have_requirement": job_info.get("job_should_have_requirement")
            if job_info.get("job_should_have_requirement")
            else "",
        }
    )
    try:
        skills_required = await insert_skills(response["skills"])
    except Exception:
        skills_required = []
    return {"skills_required": skills_required}
