from dotenv import load_dotenv
from langchain_core.prompts import Chat<PERSON>romptTemplate
from langchain_openai import Chat<PERSON>penAI
from pydantic import BaseModel, Field
from trustcall import create_extractor

from app.core.job_post.edit.prompt import EDIT_PROMPT
from app.core.job_post.edit.state import EditState

load_dotenv()


class Edit(BaseModel):
    original_content: str = Field(..., description="Original content")
    edited_content: str = Field(..., description="Edited content")


llm = ChatOpenAI(model="gpt-4o-mini", temperature=0)


async def edit_content(state: EditState):
    if state.prompt_type == "custom":
        prompt = state.prompt
    elif state.prompt_type == "longer":
        prompt = "Write longer"
    else:
        prompt = "Write shorter"

    user_prompt = (
        f"Job post: {state.job_post}\n"
        f"Content need to edit: {state.content}\n"
        f"Prompt: {prompt}\n"
    )
    messages = [
        {"role": "system", "content": EDIT_PROMPT},
        {
            "role": "user",
            "content": user_prompt,
        },
    ]
    prompt = ChatPromptTemplate.from_messages(messages=messages)
    bound = create_extractor(llm=llm, tools=[Edit], tool_choice="Edit")
    response = await bound.ainvoke(messages)
    return {"edited_content": response["responses"][0].model_dump()["edited_content"]}
