from langchain_openai import ChatOpenAI
from pydantic import BaseModel
from trustcall import create_extractor

from app.core.job_post.edit.prompt import SUGGEST_PROMPT
from app.core.job_post.edit.state import EditState


class CorrectPrompt(BaseModel):
    correct_prompt: str


llm = ChatOpenAI(model="gpt-4o-mini", temperature=0.5)


async def suggest_prompt(state: EditState):
    messages = [
        {"role": "system", "content": SUGGEST_PROMPT},
        {
            "role": "user",
            "content": state.prompt,
        },
    ]
    bound = create_extractor(
        llm=llm, tools=[CorrectPrompt], tool_choice="CorrectPrompt"
    )
    response = await bound.ainvoke(messages)
    if state.prompt != response["responses"][0].correct_prompt:
        return {
            "is_suggested": True,
            "prompt_suggested": response["responses"][0].correct_prompt,
        }

    return {
        "is_suggested": False,
        "prompt_suggested": response["responses"][0].correct_prompt,
    }
