from langgraph.graph import END, START, StateGraph

from app.core.job_post.edit.nodes import edit_content, suggest_prompt
from app.core.job_post.edit.state import EditInput, EditOutput, EditState

# Define a new graph
workflow = StateGraph(EditState, input=EditInput, output=EditOutput)


def should_suggest(state: EditState):
    if state.prompt_type == "custom" and state.prompt:
        return "suggest_prompt"
    return "edit_content"


workflow.add_node("suggest_prompt", suggest_prompt)
workflow.add_node("edit_content", edit_content)

workflow.add_conditional_edges(
    START, should_suggest, ["suggest_prompt", "edit_content"]
)
workflow.add_edge("suggest_prompt", "edit_content")
workflow.add_edge("edit_content", END)

graph = workflow.compile()
graph.name = "Job Post Edit"
