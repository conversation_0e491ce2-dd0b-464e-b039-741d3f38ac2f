from dataclasses import dataclass
from typing import Literal


@dataclass
class EditInput:
    job_post: str = ""
    content: str = ""
    prompt: str = ""
    prompt_type: Literal["longer", "shorter", "custom"] = "custom"


@dataclass
class EditState(EditInput):
    is_suggested: bool = True
    prompt_suggested: str = ""
    edited_content: str = ""


@dataclass
class EditOutput:
    is_suggested: bool
    prompt_suggested: str
    edited_content: str
