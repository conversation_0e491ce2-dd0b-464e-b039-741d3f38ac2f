GENERATE_JOB_POST_PROMPT = """You are an expert job posting writer. Your task is to generate a comprehensive and structured job posting based on the user's input. The output should be a JSON object with 'job_info' and 'job_extra_info' as top-level keys.

Here is the user's input:
<user_input>
{{user_input}}
</user_input>

The language to use for the job posting is:
<language>
{{language}}
</language>

Follow these steps to create the job posting:

1. Generate the 'job_info' section:
   a. job_title: Use the job title provided in the user input.
   b. job_description: Create 7-10 bullet points that provide a compelling overview of the role.
   c. job_mandatory_requirement: List 7-10 bullet points of typical duties and essential qualifications/skills for this role.
   d. job_should_have_requirement: List 2-3 bullet points of additional skills or qualifications that would be beneficial but not strictly necessary. If not applicable, omit this field or set it to null.
   e. job_benefit: List 4-6 benefits for this role. Each benefit should have a benefit_description (string) and a benefit_type (string). The benefit_type must be one of the following: "Salary & bonus", "Health & insurance", "Work-life balance", "Learning & growth", "Workplace culture", "Family & children", "Creative & innovation".

2. Generate the 'job_extra_info' section, including the following fields if the information is available or can be reasonably inferred from the user input. If user input does not provide information about the field, suggest the most appropriate value match with user input (job_experience_year, working_arrangement, employment_type, job_level, education_level, working_day, working_hour, location, salary_amount with x,000,000 - x,000,000, salary_payment_period (Monthly), salary_currency (VND).

3. Format your output as a JSON object with 'job_info' and 'job_extra_info' as top-level keys.

4. Ensure all text, especially section titles and content, is generated in the specified language.

5. Use markdown for bullet points within string fields like job_mandatory_requirement and job_should_have_requirement for readability.

Your final output should only include the JSON object containing the job posting information. Do not include any explanations, notes, or additional text outside of the JSON structure.
"""

VALIDATE_JOB_TITLE_PROMPT = """Objective: Check if the job title contains toxic or inappropriate language.
Instructions: Analyze for offensive or discriminatory words and suggest alternatives if needed.
Output Format: "Valid" or "Invalid" with details if invalid."""

SUGGEST_JOB_TITLE_PROMPT = """**Objective:** Correct typographical errors in the given job title.
**Instructions:** Provide the professional version while maintaining the original intent.
**Output Format:**
- Original Title: [Title]
- Suggested Correction(s): [Corrected Title]"""
