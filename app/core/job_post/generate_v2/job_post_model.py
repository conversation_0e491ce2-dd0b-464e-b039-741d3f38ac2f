from pydantic import Field

from app.core.job_post.parse.model import (
    generate_model_job_extra_info,
    generate_model_job_info,
)
from app.core.utils import _get_or_create_enum, _get_or_create_model, round_to_million
from app.helpers.get_suggest_salary import get_suggest_salary


async def postprocess_salary(job_info, job_extra_info, metadata):
    job_title = job_info["job_title"]
    data = {"data": [{"jobId": "", "jobTitle": job_title}]}
    response = await get_suggest_salary(data)
    if response:
        salary_min = response["data"][0]["minSalary"]
        salary_max = response["data"][0]["maxSalary"]
        # Round salary values to nearest million
        salary_min = round_to_million(salary_min)
        salary_max = round_to_million(salary_max)
    else:
        salary_min = 0
        salary_max = 0
    job_extra_info["salary_payment_period"] = {}
    job_extra_info["salary_currency"] = {}
    job_extra_info["salary_amount"] = {}

    # Use cached enum creation for salary payment period
    salary_payment_period_list = metadata["salaryPaymentPeriod"]
    salary_payment_period_id_enum = {
        salary_payment_period["nameEn"]: salary_payment_period["id"]
        for salary_payment_period in salary_payment_period_list
    }
    SalaryPaymentPeriodIdEnum = _get_or_create_enum(
        "SalaryPaymentPeriodIdEnum", salary_payment_period_id_enum
    )
    salary_payment_period_value = "Month"
    job_extra_info["salary_payment_period"] = {
        "salary_payment_period": salary_payment_period_value,
        "salary_payment_period_id": SalaryPaymentPeriodIdEnum[
            salary_payment_period_value
        ].value,
    }

    # Use cached enum creation for salary currency
    salary_currency_list = metadata["salaryCurrency"]
    salary_currency_id_enum = {
        salary_currency["nameEn"]: salary_currency["id"]
        for salary_currency in salary_currency_list
    }
    SalaryCurrencyIdEnum = _get_or_create_enum(
        "SalaryCurrencyIdEnum", salary_currency_id_enum
    )

    if salary_min and salary_max:
        job_extra_info["salary_amount"]["salary_min"] = salary_min
        job_extra_info["salary_amount"]["salary_max"] = salary_max

    salary_currency_value = "Vnd"
    job_extra_info["salary_currency"] = {
        "salary_currency": salary_currency_value,
        "salary_currency_id": SalaryCurrencyIdEnum[salary_currency_value].value,
    }
    return job_extra_info


async def create_generate_model(metadata):
    JobInfo = await generate_model_job_info(metadata)
    JobExtraInfo = await generate_model_job_extra_info(metadata)

    # Use cached model creation for Job model
    Job = _get_or_create_model(
        "Job",
        {
            "job_info": (JobInfo, Field(...)),
            "job_extra_info": (JobExtraInfo, Field(...)),
        },
    )
    return Job
