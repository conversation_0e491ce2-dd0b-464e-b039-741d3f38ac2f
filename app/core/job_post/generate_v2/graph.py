from langgraph.graph import END, START, StateGraph

from app.core.job_post.generate_v2.nodes import (
    generate_job_post,
    postprocess,
    suggest_job_function,
    suggest_job_title,
    suggest_skills,
    validate_job_title,
)
from app.core.job_post.generate_v2.state import (
    GenerateJobPostInput,
    GenerateJobPostOutput,
    GenerateJobPostState,
)

# Define a new graph
workflow = StateGraph(
    GenerateJobPostState, input=GenerateJobPostInput, output=GenerateJobPostOutput
)


def should_continue(state: GenerateJobPostState):
    if not state.is_valid_job_title:
        return END
    return "suggest_job_title"


workflow.add_node("validate_job_title", validate_job_title)
workflow.add_node("suggest_job_title", suggest_job_title)
workflow.add_node("generate_job_post", generate_job_post)
workflow.add_node("suggest_skills", suggest_skills)
workflow.add_node("suggest_job_function", suggest_job_function)
workflow.add_node("postprocess", postprocess)

workflow.add_edge(START, "validate_job_title")
workflow.add_conditional_edges(
    "validate_job_title", should_continue, ["suggest_job_title", END]
)
workflow.add_edge("suggest_job_title", "generate_job_post")
workflow.add_edge("generate_job_post", "suggest_skills")
workflow.add_edge("generate_job_post", "suggest_job_function")
workflow.add_edge(
    [
        "suggest_skills",
        "suggest_job_function",
    ],
    "postprocess",
)
workflow.add_edge("postprocess", END)

graph = workflow.compile()
graph.name = "Job Post Generate V2"
