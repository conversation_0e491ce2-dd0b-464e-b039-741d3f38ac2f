from app.core.job_post.generate_v2.state import GenerateJobPostState


async def postprocess(state: GenerateJobPostState):
    job_post_sections = {
        **state.job_info,
        **state.job_extra_info,
        "skills_required": state.skills_required,
        "most_related_job_function": state.most_related_job_function,
        "related_job_function": state.related_job_function,
    }
    return {
        "job_post_sections": job_post_sections,
    }
