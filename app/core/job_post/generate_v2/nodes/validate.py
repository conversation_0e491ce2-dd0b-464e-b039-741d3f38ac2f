from dotenv import load_dotenv
from langchain_openai import ChatOpenAI

from app.core.job_post.generate_v2.prompt import VALIDATE_JOB_TITLE_PROMPT
from app.core.job_post.generate_v2.state import GenerateJobPostState
from app.core.job_post.generate_v2.validate_job_title import ValidateJobTitle

load_dotenv()

llm = ChatOpenAI(model="gpt-4o-mini", temperature=0)


async def validate_job_title(state: GenerateJobPostState):
    messages = [
        {"role": "system", "content": VALIDATE_JOB_TITLE_PROMPT},
        {
            "role": "user",
            "content": state.job_title,
        },
    ]
    chain = llm.with_structured_output(ValidateJobTitle)
    response = await chain.ainvoke(messages)
    return {"is_valid_job_title": response.is_validity}
