from langgraph.graph import END, START, StateGraph

from app.core.job_post.suggest_skills.nodes import suggest_skills
from app.core.job_post.suggest_skills.state import (
    SuggestSkillsInput,
    SuggestSkillsOutput,
    SuggestSkillsState,
)

# Define a new graph
workflow = StateGraph(
    SuggestSkillsState,
    input=SuggestSkillsInput,
    output=SuggestSkillsOutput,
)


workflow.add_node("suggest_skills", suggest_skills)

workflow.add_edge(START, "suggest_skills")
workflow.add_edge("suggest_skills", END)

graph = workflow.compile()
graph.name = "Job Post Suggest Skills"
