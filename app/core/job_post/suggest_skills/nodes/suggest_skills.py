from typing import List

from langchain_openai import ChatOpenAI
from pydantic import BaseModel, Field

from app.core.job_post.suggest_skills.prompt import SUGGEST_SKILLS_PROMPT
from app.core.job_post.suggest_skills.state import SuggestSkillsState


class SuggestSkills(BaseModel):
    skills: List[str] = Field(default_factory=list, description="List skills")


llm = ChatOpenAI(model="gpt-4.1-nano", temperature=0.5)
chain = llm.with_structured_output(SuggestSkills)


async def suggest_skills(state: SuggestSkillsState):
    user_prompt = (
        f"Job title: {state.job_title}\n"
        f"Job description: \n{state.job_description}\n"
        f"Job mandatory requirement: \n{state.job_mandatory_requirement}\n"
        f"Job should have requirement: \n{state.job_should_have_requirement}"
    )
    messages = [
        {"role": "system", "content": SUGGEST_SKILLS_PROMPT},
        {
            "role": "user",
            "content": user_prompt,
        },
    ]
    response = await chain.ainvoke(messages)
    return {"skills": response.model_dump()["skills"]}
