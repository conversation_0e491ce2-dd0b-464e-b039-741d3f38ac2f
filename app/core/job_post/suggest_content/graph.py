from langgraph.graph import END, START, StateGraph

from app.core.job_post.suggest_content.nodes import suggest
from app.core.job_post.suggest_content.state import (
    SuggestInput,
    SuggestOutput,
    SuggestState,
)

workflow = StateGraph(SuggestState, input=SuggestInput, output=SuggestOutput)

workflow.add_node("suggest", suggest)

workflow.add_edge(START, "suggest")
workflow.add_edge("suggest", END)

graph = workflow.compile()
