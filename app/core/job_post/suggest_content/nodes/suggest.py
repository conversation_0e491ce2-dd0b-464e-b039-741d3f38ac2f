import re

from langchain_openai import ChatOpenAI
from pydantic import BaseModel
from trustcall import create_extractor

from app.core.job_post.suggest_content.prompt import SUGGEST_PROMPT
from app.core.job_post.suggest_content.state import SuggestState
from app.core.job_post.suggest_content.utils import postprocess_content_suggest
from app.core.utils import round_to_million
from app.helpers.get_suggest_salary import get_suggest_salary


class SuggestContent(BaseModel):
    job_title: str
    job_description: str
    job_mandatory_requirement: str
    job_should_have_requirement: str


llm = ChatOpenAI(model="gpt-4o-mini", temperature=1.0)
bound = create_extractor(llm=llm, tools=[SuggestContent], tool_choice="SuggestContent")


async def suggest(state: SuggestState):
    user_prompt = (
        f"Job title: {state.job_title}\n"
        f"Job description: \n{state.job_description}\n"
        f"Job mandatory requirement: \n{state.job_mandatory_requirement}\n"
        f"Job should have requirement: \n{state.job_should_have_requirement}"
    )
    messages = [
        {"role": "system", "content": SUGGEST_PROMPT},
        {"role": "user", "content": user_prompt},
    ]

    response = await bound.ainvoke(messages)
    job_post_suggest = response["responses"][0].model_dump(mode="json")
    new_job_post_suggest = postprocess_content_suggest(job_post_suggest, state.__dict__)

    # Clean job title: remove text in parentheses and special characters
    cleaned_job_title = re.sub(
        r"\([^)]*\)", "", state.job_title
    )  # Remove text in parentheses
    cleaned_job_title = re.sub(
        r"[^\w\s]", " ", cleaned_job_title
    )  # Remove special characters, keep letters, numbers, spaces
    cleaned_job_title = " ".join(cleaned_job_title.split())  # Remove extra spaces
    print(cleaned_job_title)
    data = {"data": [{"jobId": "", "jobTitle": cleaned_job_title}]}
    response = await get_suggest_salary(data)
    if response:
        salary_min = response["data"][0]["minSalary"]
        salary_max = response["data"][0]["maxSalary"]
        salary_min = round_to_million(salary_min)
        salary_max = round_to_million(salary_max)
        salary = {
            "salary_min": float(salary_min),
            "salary_max": float(salary_max),
            "salary_currency": "Vnd",
        }
    else:
        salary = {}
    new_job_post_suggest.update({"salary": salary})
    return {"job_post_suggest": new_job_post_suggest}
