from dataclasses import dataclass, field
from typing import List


@dataclass
class SuggestJobFunctionInput:
    job_title: str
    job_description: str
    job_mandatory_requirement: str
    job_should_have_requirement: str
    list_job_function: List[dict]


@dataclass
class SuggestJobFunctionState(SuggestJobFunctionInput):
    most_related_job_function: dict = field(default_factory=dict)
    related_job_function: List[dict] = field(default_factory=list)


@dataclass
class SuggestJobFunctionOutput:
    most_related_job_function: dict
    related_job_function: List[dict]
