from langgraph.graph import END, START, StateGraph

from app.core.job_post.suggest_job_function.nodes import suggest_job_function
from app.core.job_post.suggest_job_function.state import (
    SuggestJobFunctionInput,
    SuggestJobFunctionState,
)

# Define a new graph
workflow = StateGraph(
    SuggestJobFunctionState,
    input=SuggestJobFunctionInput,
    output=SuggestJobFunctionState,
)


workflow.add_node("suggest_job_function", suggest_job_function)

workflow.add_edge(START, "suggest_job_function")
workflow.add_edge("suggest_job_function", END)

graph = workflow.compile()
graph.name = "Job Post Suggest Job Function"
