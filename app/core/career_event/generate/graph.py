from langgraph.graph import END, StateGraph

from app.core.career_event.generate.nodes.generate import generate_career_event
from app.core.career_event.generate.state import (
    GenerateCareerEventInput,
    GenerateCareerEventOutput,
    GenerateCareerEventState,
)

# Define a new graph
workflow = StateGraph(
    GenerateCareerEventState,
    input=GenerateCareerEventInput,
    output=GenerateCareerEventOutput,
)

workflow.add_node("generate_career_event", generate_career_event)

workflow.set_entry_point("generate_career_event")
workflow.add_edge("generate_career_event", END)

graph = workflow.compile()
graph.name = "Career Event Generate"
