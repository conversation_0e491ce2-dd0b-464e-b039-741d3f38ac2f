from dataclasses import dataclass, field
from typing import Literal


@dataclass
class GenerateCareerEventInput:
    event_title: str
    event_description: str = ""
    event_time: str = field(default="")
    event_location: str = field(default="")
    language: Literal["Vietnamese", "English"] = "Vietnamese"
    company_information: str = ""


@dataclass
class GenerateCareerEventState(GenerateCareerEventInput):
    career_event_generated: str = ""


@dataclass
class GenerateCareerEventOutput:
    career_event_generated: str
