import aiofiles
from dotenv import load_dotenv
from langchain_openai import ChatOpenAI
from langgraph.prebuilt import create_react_agent

from app.core.career_event.generate.state import GenerateCareerEventState
from app.core.tools import TOOLS

load_dotenv()

llm = ChatOpenAI(model="gpt-4o-mini", temperature=1.0)


async def generate_career_event(state: GenerateCareerEventState):
    async with aiofiles.open("core/prompts/generate_career_event.txt", "r") as f:
        system_prompt = await f.read()

    user_prompt = f"""Event title: {state.event_title}
{"Event description: " + state.event_description if state.event_description else ""}
Event time: {state.event_time}
Event location: {state.event_location}
Language: {state.language}
Writing style: {state.writing_style}
{"Company information: " + state.company_information if state.company_information else ""}
"""
    messages = [
        {"role": "user", "content": user_prompt},
    ]
    agent = create_react_agent(llm, tools=TOOLS, prompt=system_prompt)
    response = await agent.ainvoke({"messages": messages})
    return {"career_event_generated": response["messages"][-1].content}
