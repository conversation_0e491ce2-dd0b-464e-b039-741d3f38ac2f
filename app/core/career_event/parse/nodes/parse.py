from dotenv import load_dotenv
from langchain_openai import ChatOpenAI
from trustcall import create_extractor

from app.core.career_event.parse.models.career_event import CareerEvent
from app.core.career_event.parse.state import ParseCareerEventState

load_dotenv()

llm = ChatOpenAI(model="gpt-4o-mini", temperature=0)


async def parse_career_event(state: ParseCareerEventState):
    career_event_content = state.career_event_content
    if career_event_content == "":
        return {"career_event_parsed": {}}

    bound = create_extractor(
        llm,
        tools=[CareerEvent],
        tool_choice="CareerEvent",
    )
    response = await bound.ainvoke(career_event_content)
    return {"career_event_parsed": response["responses"][0].model_dump()}
