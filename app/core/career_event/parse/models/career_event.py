from typing import Optional

from pydantic import BaseModel, Field


class CareerEvent(BaseModel):
    event_title: str = Field(..., description="The name or title of the career event")
    event_description: str = Field(
        ..., description="A detailed description of what the event is about"
    )
    event_time: str = Field(
        ...,
        description="Date and time when the event will take place, format: hh:mm dd/Mm/yyyy - hh:mm dd/Mm/yyyy",
    )
    event_location: str = Field(
        ..., description="Physical or virtual location where the event will be held"
    )
    participant_benefit: Optional[str] = Field(
        None, description="Benefits that participants will receive from attending"
    )
    event_agenda: Optional[str] = Field(
        None, description="Schedule or outline of activities during the event"
    )
    company_profile: Optional[str] = Field(
        None, description="Information about the organizing company or companies"
    )
    event_image: Optional[str] = Field(
        None, description="URL or reference to event promotional image"
    )
    participant_requirement: Optional[str] = Field(
        None, description="Specific requirements or qualifications for participants"
    )
    participant_school: Optional[str] = Field(
        None, description="Target schools or educational institutions"
    )
    participant_major: Optional[str] = Field(
        None, description="Required or preferred academic majors"
    )
    event_industry: Optional[str] = Field(
        None, description="Industry sectors relevant to the event"
    )
