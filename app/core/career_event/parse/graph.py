from langgraph.graph import END, StateGraph

from app.core.career_event.parse.nodes.parse import parse_career_event
from app.core.career_event.parse.state import (
    ParseCareerEventInput,
    ParseCareerEventOutput,
    ParseCareerEventState,
)

workflow = StateGraph(
    ParseCareerEventState, input=ParseCareerEventInput, output=ParseCareerEventOutput
)

workflow.add_node("parse_career_event", parse_career_event)

workflow.set_entry_point("parse_career_event")
workflow.add_edge("parse_career_event", END)

graph = workflow.compile()
graph.name = "Career Event Parse"
