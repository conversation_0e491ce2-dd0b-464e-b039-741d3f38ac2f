import hashlib
import re
import weakref
from enum import Enum

from pydantic import create_model

# Global cache for created enums and models to prevent memory leaks
_enum_cache = weakref.WeakValueDictionary()
_model_cache = weakref.WeakValueDictionary()


def sanitize_name(name: str) -> str:
    """Convert a name to a valid format for the API."""
    # Replace spaces and special chars with underscores, keep alphanumeric
    sanitized = re.sub(r"[^a-zA-Z0-9_-]", "_", name)
    return sanitized


def round_to_million(value):
    """Round salary value to the nearest million"""
    if value == 0:
        return 0
    return round(value / 1000000) * 1000000


def _create_cache_key(name: str, data: dict) -> str:
    """Create a cache key based on enum name and data content"""
    data_str = str(sorted(data.items()))
    return hashlib.md5(f"{name}:{data_str}".encode(), usedforsecurity=False).hexdigest()


def _get_or_create_enum(name: str, enum_dict: dict):
    """Get existing enum from cache or create new one"""
    if not enum_dict:
        # Return a default enum with empty values to prevent errors
        return Enum(name, {"NONE": "NONE"})

    cache_key = _create_cache_key(name, enum_dict)

    if cache_key in _enum_cache:
        return _enum_cache[cache_key]

    # Create new enum and cache it
    new_enum = Enum(name, enum_dict)
    _enum_cache[cache_key] = new_enum
    return new_enum


def _get_or_create_model(name: str, fields: dict):
    """Get existing model from cache or create new one"""
    # Create a cache key based on model name and field definitions
    fields_str = str(sorted([(k, str(v)) for k, v in fields.items()]))
    cache_key = hashlib.md5(
        f"{name}:{fields_str}".encode(), usedforsecurity=False
    ).hexdigest()

    if cache_key in _model_cache:
        return _model_cache[cache_key]

    # Create new model and cache it
    new_model = create_model(name, **fields)
    _model_cache[cache_key] = new_model
    return new_model


def clear_model_cache():
    """Clear the model and enum cache - useful for testing or memory management"""
    global _enum_cache, _model_cache
    _enum_cache.clear()
    _model_cache.clear()
