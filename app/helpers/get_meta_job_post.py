import os

import httpx
from dotenv import load_dotenv
from tenacity import retry, stop_after_attempt, wait_fixed

from app.utils import alru_cache_non_empty

load_dotenv()

META_API = os.getenv("META_API")


@alru_cache_non_empty()
@retry(stop=stop_after_attempt(3), wait=wait_fixed(2))
async def get_meta_job_post():
    try:
        async with httpx.AsyncClient(timeout=httpx.Timeout(timeout=1)) as client:
            response = await client.get(url=META_API)
            if response.status_code == 200:
                return response.json()["data"]
            return []
    except Exception:
        return []
