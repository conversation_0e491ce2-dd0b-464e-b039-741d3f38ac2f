import os
from base64 import b64encode

import httpx
from dotenv import load_dotenv
from fastapi import UploadFile
from tenacity import retry, stop_after_attempt, wait_fixed

from app.exceptions import FileError

load_dotenv()

TEXTRACT_API = os.getenv("TEXTRACT_API")


@retry(stop=stop_after_attempt(3), wait=wait_fixed(2))
async def get_content_from_file(file: UploadFile):
    byte_content = await file.read()
    filename = file.filename
    base64_string = b64encode(byte_content).decode("utf-8")
    raw_data = {
        "data": base64_string,
        "file_type": os.path.splitext(filename)[1][1:],
    }
    async with httpx.AsyncClient(timeout=httpx.Timeout(timeout=30)) as client:
        response = await client.post(url=TEXTRACT_API, json=raw_data)

        if response.status_code == 200:
            content = response.json().get("text", "")
            return content
        else:
            raise FileError("Get content from file error!")
