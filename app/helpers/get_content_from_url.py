import os

from crawl4ai import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, BrowserConfig, CrawlerRunConfig
from dotenv import load_dotenv
from tenacity import retry, stop_after_attempt, wait_fixed

from app.exceptions import URLError

load_dotenv()


@retry(stop=stop_after_attempt(3), wait=wait_fixed(2))
async def get_content_from_url(url: str):
    proxy_server = os.getenv("PROXY_SERVER")
    proxy_username = os.getenv("PROXY_USERNAME")
    proxy_password = os.getenv("PROXY_PASSWORD")
    if proxy_server and proxy_username and proxy_password:
        proxy_config = {
            "server": proxy_server,
            "username": proxy_username,
            "password": proxy_password,
        }
    else:
        proxy_config = None
    browser_config = BrowserConfig(
        verbose=True,
        proxy_config=proxy_config,
        java_script_enabled=False,
        user_agent="(KHTML, like Gecko) Chrome/116.0.5845.187 Safari/604.1 Edg/117.0.2045.47",
        text_mode=True,
    )
    run_config = CrawlerRunConfig(
        excluded_tags=["script", "style", "img", "a", "button", "footer"],
        disable_cache=True,
        remove_forms=True,
        prettiify=True,
        verbose=True,
    )
    async with AsyncWebCrawler(config=browser_config) as crawler:
        result = await crawler.arun(url=url, config=run_config)
        if result.success:
            return result.markdown
        else:
            raise URLError("Get content from URL error!")
