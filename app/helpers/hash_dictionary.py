import hashlib
import json


def hash_dictionary(input_dict):
    # Sort the dictionary by keys to ensure consistent hashing
    sorted_dict = {k: input_dict[k] for k in sorted(input_dict.keys())}

    # Convert dictionary to a JSON string
    json_str = json.dumps(sorted_dict, sort_keys=True)

    # Create hash
    hash_object = hashlib.md5(json_str.encode(), usedforsecurity=False)

    # Return hexadecimal representation of hash
    return hash_object.hexdigest()
