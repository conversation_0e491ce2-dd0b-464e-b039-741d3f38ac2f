import os

import httpx
from dotenv import load_dotenv

from app.exceptions import InsertSkillsError

load_dotenv()

CAREER_RESOURCES_API = os.getenv("CAREER_RESOURCES_API")


async def insert_skills(skills: list[str]) -> list[str]:
    """
    Insert skills into the career resources
    """
    list_skills_input = []
    for skill in skills:
        list_skills_input.append({"name": skill})

    payload = {"objects": list_skills_input}

    async with httpx.AsyncClient(timeout=httpx.Timeout(timeout=10)) as client:
        response = await client.post(
            f"{CAREER_RESOURCES_API}/internal/v1/skills", json=payload
        )
        if response.status_code != 200:
            raise InsertSkillsError(f"Failed to insert skills: {response.text}")
        skills_required = []
        for skill in response.json()["data"]:
            skills_required.append(
                {"skill_id": skill["id"], "skill_name": skill["name"]}
            )
        return skills_required
