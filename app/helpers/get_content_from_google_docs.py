import io
from urllib.parse import urlparse, urlunparse

import gdown
from docx import Document
from tenacity import retry, stop_after_attempt, wait_fixed


def remove_url_params(url):
    """
    Remove all query parameters from a URL.

    Args:
        url (str): The URL to process

    Returns:
        str: URL with all query parameters removed
    """
    parsed_url = urlparse(url)
    # Set the query component to an empty string
    clean_url = urlunparse(
        (
            parsed_url.scheme,
            parsed_url.netloc,
            parsed_url.path,
            parsed_url.params,
            "",  # Empty query string
            parsed_url.fragment,
        )
    )
    return clean_url


@retry(stop=stop_after_attempt(3), wait=wait_fixed(2))
def get_content_from_google_docs(doc_url):
    """
    Read a public Google Doc by exporting it as DOCX and parsing
    Requires the direct sharing URL (with /edit)
    """
    # Convert from /edit URL to export format
    cleaned_doc_url = remove_url_params(doc_url)
    export_url = cleaned_doc_url.replace("/edit", "/export?format=docx")

    # Download the file to memory
    output = io.BytesIO()
    gdown.download(export_url, output, quiet=True, fuzzy=True)
    output.seek(0)

    # Parse the docx file
    doc = Document(output)
    full_text = []

    for para in doc.paragraphs:
        full_text.append(para.text)

    content = "\n".join(full_text)
    return content
