import os

import httpx
from dotenv import load_dotenv
from tenacity import retry, stop_after_attempt, wait_fixed

from app.utils import alru_cache_non_empty

load_dotenv()

LOCATION_API = os.getenv("LOCATION_API")


@alru_cache_non_empty()
@retry(stop=stop_after_attempt(3), wait=wait_fixed(2))
async def get_country():
    try:
        async with httpx.AsyncClient(timeout=httpx.Timeout(timeout=1)) as client:
            response = await client.get(
                url=f"{LOCATION_API}/country/list?userId=12de50b8-91bb-4b82-8af5-94f97d66d58c"
            )
            if response.status_code == 200:
                return response.json()["data"]
            return []
    except Exception:
        return []


@alru_cache_non_empty()
@retry(stop=stop_after_attempt(3), wait=wait_fixed(2))
async def get_city(country_id: str):
    try:
        async with httpx.AsyncClient(timeout=httpx.Timeout(timeout=1)) as client:
            response = await client.get(
                url=f"{LOCATION_API}/country/{country_id}?userId=12de50b8-91bb-4b82-8af5-94f97d66d58c"
            )
            if response.status_code == 200:
                return response.json()["data"]
            return []
    except Exception:
        return []


@alru_cache_non_empty()
@retry(stop=stop_after_attempt(3), wait=wait_fixed(2))
async def get_district(city_id: str):
    try:
        async with httpx.AsyncClient(timeout=httpx.Timeout(timeout=1)) as client:
            response = await client.get(
                url=f"{LOCATION_API}/city/{city_id}?userId=12de50b8-91bb-4b82-8af5-94f97d66d58c"
            )
            if response.status_code == 200:
                return response.json()["data"]
            return []
    except Exception:
        return []
