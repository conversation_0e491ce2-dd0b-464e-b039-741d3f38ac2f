import os

import httpx

SALARY_API = os.getenv("SALARY_API")


async def get_suggest_salary(data):
    try:
        async with httpx.AsyncClient() as client:
            response = await client.post(url=SALARY_API, json=data, timeout=3)
            if response.status_code == 200:
                return response.json()
            else:
                return None
    except Exception:
        return None
