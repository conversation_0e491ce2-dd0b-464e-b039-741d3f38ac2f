from fastapi import APIRouter, status
from fastapi.responses import JSONResponse

from app.api.routers import create_error_response, create_response
from app.core.job_post import GenerateJobPostInput
from app.handlers import job_post_generate as job_post_generate_handler

job_post_router = APIRouter(prefix="/internal/v1/job-post", tags=["Job Post"])


@job_post_router.post("/generate")
async def job_post_generate(data: GenerateJobPostInput):
    try:
        error, response = await job_post_generate_handler(data=data)
        if not response:
            return create_error_response(
                status.HTTP_400_BAD_REQUEST,
                {
                    "message": "Failed to generate job post. Please provide a valid job title and try again.",
                    "suggest_job_title": None,
                },
            )
        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content=create_response(
                status.HTTP_200_OK,
                data=response,
                error={
                    "message": "Typographical error in job title" if error else None,
                    "suggest_job_title": error if error else None,
                },
            ),
        )
    except Exception as e:
        return create_error_response(
            status.HTTP_500_INTERNAL_SERVER_ERROR,
            {
                "message": f"An error occurred while generating the content: {str(e)}",
                "suggest_job_title": None,
            },
        )
