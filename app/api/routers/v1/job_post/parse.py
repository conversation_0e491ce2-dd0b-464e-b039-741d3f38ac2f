from typing import Optional, <PERSON><PERSON>

from dotenv import load_dotenv
from fastapi import APIRouter, Request, UploadFile, status
from fastapi.concurrency import run_in_threadpool
from fastapi.responses import JSONResponse
from tenacity import RetryError

from app.api.routers import create_error_response, create_response
from app.core.job_post import ParseJobPostInput
from app.exceptions import ParseError
from app.handlers import job_post_parse as job_post_parse_handler
from app.helpers import (
    get_content_from_file,
    get_content_from_google_docs,
    get_content_from_url,
)

load_dotenv()

job_post_router = APIRouter(prefix="/internal/v1/job-post", tags=["Job Post"])


async def get_content_from_request(
    request: Request, file: UploadFile = None
) -> Tuple[Optional[str], Optional[JSONResponse]]:
    form = await request.form()
    if form and file:
        if file.size > 5 * 1024 * 1024:  # 5MB limit
            return None, create_error_response(
                status.HTTP_400_BAD_REQUEST,
                "File size exceeds the maximum limit of 5MB.",
            )
        try:
            return await get_content_from_file(file), None
        except RetryError:
            return None, create_error_response(
                status.HTTP_400_BAD_REQUEST,
                "Failed to process the uploaded file. Please ensure the file is not corrupted and try again.",
            )
    try:
        data = await request.json()
    except Exception:
        return None, create_error_response(
            status.HTTP_400_BAD_REQUEST,
            "Invalid JSON format in request body. Please check the request format and try again.",
        )
    if "content" in data:
        return data["content"], None
    elif "url" in data:
        try:
            if data["url"].startswith("https://docs.google.com/document/d"):
                return await run_in_threadpool(
                    get_content_from_google_docs, data["url"]
                ), None
            else:
                return await get_content_from_url(data["url"]), None
        except RetryError:
            return None, create_error_response(
                status.HTTP_400_BAD_REQUEST,
                "Failed to fetch content from the provided URL. Please verify the URL is accessible and try again.",
            )

    return None, create_error_response(
        status.HTTP_400_BAD_REQUEST,
        "Invalid request format. Please provide either 'content' in the request body or a valid 'url' to parse.",
    )


@job_post_router.post("/parse", response_model=dict)
async def job_post_parse(request: Request, file: UploadFile = None) -> JSONResponse:
    content, error_response = await get_content_from_request(request, file)
    if error_response:
        return error_response
    try:
        # Check if the response is cached
        data = ParseJobPostInput(content=content)
        response = await job_post_parse_handler(data=data)
        if not response:
            return create_error_response(
                status.HTTP_400_BAD_REQUEST,
                "Invalid content. Please ensure the content follows the required structure.",
            )
        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content=create_response(
                status.HTTP_200_OK,
                data=response,
            ),
        )
    except ParseError as e:
        return create_error_response(
            status.HTTP_500_INTERNAL_SERVER_ERROR,
            f"An error occurred while parsing the content: {str(e)}",
        )
