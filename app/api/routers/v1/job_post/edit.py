import re

from fastapi import <PERSON>Router, status
from fastapi.responses import JSONResponse

from app.api.routers import create_error_response, create_response
from app.core.job_post import EditInput
from app.handlers import job_post_edit as job_post_edit_handler

job_post_router = APIRouter(prefix="/internal/v1/job-post", tags=["Job Post"])


@job_post_router.post("/edit")
async def job_post_edit(data: EditInput):
    try:
        character_count = len(re.findall("[a-zA-Z0-9]", data.content))
        if character_count <= 1:
            return create_error_response(
                status.HTTP_400_BAD_REQUEST,
                {
                    "message": "Content must be longer than 1 character",
                    "suggest_prompt": None,
                },
            )
        error, response = await job_post_edit_handler(data=data)
        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content=create_response(
                status.HTTP_200_OK,
                data=response["edited_content"],
                error={
                    "message": "Typographical error in user prompt" if error else None,
                    "suggest_prompt": error if error else None,
                },
            ),
        )
    except Exception as e:
        return create_error_response(
            status.HTTP_500_INTERNAL_SERVER_ERROR,
            {
                "message": f"An error occurred while editing the content: {str(e)}",
                "suggest_job_title": None,
            },
        )
