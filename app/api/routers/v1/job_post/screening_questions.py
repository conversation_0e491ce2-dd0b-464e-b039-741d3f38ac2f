from fastapi import APIRouter, status
from fastapi.responses import JSONResponse

from app.api.routers import create_error_response, create_response
from app.core.job_post.screening_questions.state import ScreeningQuestionsInput
from app.handlers import screening_questions as screening_questions_handler

job_post_router = APIRouter(prefix="/internal/v1/job-post", tags=["Job Post"])


@job_post_router.post("/screening-questions")
async def job_screening_questions(data: ScreeningQuestionsInput):
    try:
        response = await screening_questions_handler(data=data.__dict__)
        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content=create_response(status.HTTP_200_OK, data=response),
        )
    except Exception as e:
        return create_error_response(status.HTTP_500_INTERNAL_SERVER_ERROR, str(e))
