from fastapi.responses import JSONResponse


def create_response(code: int, error: dict | str = None, data: dict = None) -> dict:
    return {"code": code, "error": error, "data": data or None, "metadata": {}}


def create_error_response(status_code: int, error_message: str) -> JSONResponse:
    return JSONResponse(
        status_code=status_code, content=create_response(status_code, error_message)
    )
