import json
import logging
import os
from typing import Any, Optional

from dotenv import load_dotenv

from app.services.redis import redis_service

load_dotenv()

logger = logging.getLogger(__name__)


class CacheService:
    """Cache service with Redis fallback functionality and memory optimization."""

    def __init__(self):
        self.redis_expire_time = (
            int(os.getenv("REDIS_EXPIRE_TIME", "30")) * 60 * 60 * 24
        )  # Convert days to seconds

    async def get(self, key: str) -> Optional[Any]:
        """
        Get value from cache.
        Returns None if key doesn't exist or Redis is unavailable.
        """
        try:
            cached_data = await redis_service.get(key)
            if cached_data:
                # Decode bytes to string before JSON parsing
                if isinstance(cached_data, bytes):
                    cached_data = cached_data.decode("utf-8")
                return json.loads(cached_data)
            return None
        except Exception as e:
            logger.warning(f"Redis get operation failed for key '{key}': {str(e)}")
            return None

    async def set(
        self, key: str, value: Any, expire_time: Optional[int] = None
    ) -> bool:
        """
        Set value in cache.
        Returns True if successful, False if Red<PERSON> is unavailable.
        """
        try:
            expire_time = expire_time or self.redis_expire_time
            # Use separators to minimize JSON size
            json_data = json.dumps(value, separators=(",", ":"), ensure_ascii=False)
            await redis_service.set(key, json_data, ex=expire_time)
            return True
        except Exception as e:
            logger.warning(f"Redis set operation failed for key '{key}': {str(e)}")
            return False

    async def delete(self, key: str) -> bool:
        """
        Delete value from cache.
        Returns True if successful, False if Redis is unavailable.
        """
        try:
            await redis_service.delete(key)
            return True
        except Exception as e:
            logger.warning(f"Redis delete operation failed for key '{key}': {str(e)}")
            return False

    async def cleanup(self):
        """Cleanup cache service resources."""
        await redis_service.close()


# Global cache service instance
cache_service = CacheService()
