from sentry_sdk import capture_exception

from app.core.job_post.screening_questions.graph import (
    graph as screening_questions_graph,
)
from app.exceptions import ScreeningQuestionsError


async def screening_questions(data: dict):
    try:
        request = {
            "job_title": data.get("job_title"),
            "job_description": data.get("job_description"),
            "job_mandatory_requirement": data.get("job_mandatory_requirement"),
            "job_should_have_requirement": data.get("job_should_have_requirement"),
            "skills": data.get("skills"),
            "location": data.get("location"),
        }
        response = await screening_questions_graph.ainvoke(request)
        return response["screening_questions"]
    except Exception as e:
        capture_exception(e)
        raise ScreeningQuestionsError(str(e))
