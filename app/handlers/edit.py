from sentry_sdk import capture_exception

from app.core import job_post_edit_graph
from app.core.job_post.edit.state import EditInput
from app.exceptions import EditError
from app.services.langfuse import get_langfuse_handler


async def job_post_edit(data: EditInput):
    try:
        response = await job_post_edit_graph.ainvoke(
            data,
            config={"callbacks": [get_langfuse_handler()], "run_name": "Job Edit"},
        )
        if not response:
            return None, None
        if response.get("is_suggested"):
            return (
                response.get("prompt_suggested"),
                response,
            )
        return None, response
    except Exception as e:
        capture_exception(e)
        raise EditError(str(e))
