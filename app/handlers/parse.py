from sentry_sdk import capture_exception

from app.core import job_post_parse_graph, job_post_parse_v2_graph
from app.core.job_post import ParseJobPostInput
from app.exceptions import ParseError
from app.helpers import get_city, get_country, get_meta_job_post, hash_dictionary
from app.services.cache import cache_service
from app.services.langfuse import get_langfuse_handler


async def job_post_parse(data: ParseJobPostInput):
    try:
        # Create cache key based on input data
        cache_data = {
            "content": data.content,
            "metadata": data.metadata,
        }
        cache_key = f"job_post_parse_{hash_dictionary(cache_data)}"

        # Try to get cached response
        cached_response = await cache_service.get(cache_key)
        if cached_response:
            return cached_response

        # If not cached, generate new response
        metadata = await get_meta_job_post()
        country_list = await get_country()
        if country_list and len(country_list) > 0:
            country_id = country_list[0]["id"]
            city = await get_city(country_id)
            if metadata and city:
                metadata.update({"city": city})
                data.metadata = metadata
        elif metadata:
            data.metadata = metadata
        response = await job_post_parse_graph.ainvoke(
            data,
            config={"callbacks": [get_langfuse_handler()], "run_name": "Job Parser"},
        )
        if not response:
            return None

        result = {
            "parsed": response["job_post_parsed"],
            "suggest": response["job_post_suggest"],
        }

        # Cache the response (fire and forget - don't fail if caching fails)
        await cache_service.set(cache_key, result)

        return result
    except Exception as e:
        capture_exception(e)
        raise ParseError(str(e))


async def job_post_parse_v2(data: ParseJobPostInput):
    try:
        # Create cache key based on input data
        cache_data = {
            "content": data.content,
            "metadata": data.metadata,
        }
        cache_key = f"job_post_parse_v2_{hash_dictionary(cache_data)}"

        # Try to get cached response
        cached_response = await cache_service.get(cache_key)
        if cached_response:
            return cached_response

        # If not cached, generate new response
        metadata = await get_meta_job_post()
        country_list = await get_country()
        if country_list and len(country_list) > 0:
            country_id = country_list[0]["id"]
            city = await get_city(country_id)
            if metadata and city:
                metadata.update({"city": city})
                data.metadata = metadata
        elif metadata:
            data.metadata = metadata
        response = await job_post_parse_v2_graph.ainvoke(
            data,
            config={"callbacks": [get_langfuse_handler()], "run_name": "Job Parser V2"},
        )
        if not response:
            return None

        result = {"parsed": response["job_post_parsed"]}

        # Cache the response (fire and forget - don't fail if caching fails)
        await cache_service.set(cache_key, result)

        return result
    except Exception as e:
        capture_exception(e)
        raise ParseError(str(e))
