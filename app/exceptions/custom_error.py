class EditError(Exception):
    def __init__(self, message: str):
        self.message = message
        super().__init__(self.message)


class FileError(Exception):
    def __init__(self, message: str):
        self.message = message
        super().__init__(self.message)


class GenerateError(Exception):
    def __init__(self, message: str):
        self.message = message
        super().__init__(self.message)


class ParseError(Exception):
    def __init__(self, message: str):
        self.message = message
        super().__init__(self.message)


class SalaryError(Exception):
    def __init__(self, message: str):
        self.message = message
        super().__init__(self.message)


class ScreeningQuestionsError(Exception):
    def __init__(self, message: str):
        self.message = message
        super().__init__(self.message)


class SuggestError(Exception):
    def __init__(self, message: str):
        self.message = message
        super().__init__(self.message)


class URLError(Exception):
    def __init__(self, message: str):
        self.message = message
        super().__init__(self.message)


class InsertSkillsError(Exception):
    def __init__(self, message: str):
        self.message = message
        super().__init__(self.message)
