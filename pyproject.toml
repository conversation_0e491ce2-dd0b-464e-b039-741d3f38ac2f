[tool.poetry]
name = "upzi-opportunity-creation"
version = "1.0.0"
description = ""
authors = ["<PERSON> Trung <PERSON> <<EMAIL>>"]
readme = "README.md"

[tool.poetry.dependencies]
python = "^3.12"
langgraph = "^0.2.73"
langchain = "^0.3.18"
langchain-openai = "^0.3.6"
langchain-community = "^0.3.17"
crawl4ai = "^0.4.248"
playwright = "^1.50.0"
duckduckgo-search = "7.3.2"
fastapi = "^0.115.8"
uvicorn = "^0.34.0"
sentry-sdk = "^2.22.0"
trustcall = "^0.0.37"
python-multipart = "^0.0.20"
langid = "^1.1.6"
gdown = "^5.2.0"
python-docx = "^1.1.2"
langfuse = "^2.60.1"
redis = "^5.2.1"
async-lru = "^2.0.5"


[tool.poetry.group.dev.dependencies]
ipykernel = "^6.29.5"
pre-commit = "^4.1.0"
pytest = "^8.3.4"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
