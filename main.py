import logging
import os
from contextlib import asynccontextmanager

import sentry_sdk
import uvicorn
from dotenv import load_dotenv
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from sentry_sdk.integrations.fastapi import FastApiIntegration
from sentry_sdk.integrations.starlette import StarletteIntegration

from app.api.routers.health import health_router
from app.api.routers.v1.job_post import (
    edit_router,
    generate_router,
    parse_router,
    screening_questions_router,
)
from app.api.routers.v2.job_post import generate_router_v2, parse_router_v2
from app.services.cache import cache_service
from app.services.redis import cleanup_redis
from app.utils import LOGGING_CONFIG

load_dotenv()

os.makedirs("logs", exist_ok=True)
logging.config.dictConfig(LOGGING_CONFIG)

sentry_sdk.init(
    dsn=os.getenv("SENTRY_DSN", ""),
    traces_sample_rate=0.1,
    integrations=[
        StarletteIntegration(),
        FastApiIntegration(),
    ],
)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager for proper resource cleanup."""
    # Startup
    logger = logging.getLogger(__name__)
    logger.info("Application starting up...")

    yield

    # Shutdown
    logger.info("Application shutting down...")
    try:
        await cleanup_redis()
        await cache_service.cleanup()
        logger.info("Redis connections closed successfully")
    except Exception as e:
        logger.error(f"Error during shutdown: {e}")


app = FastAPI(lifespan=lifespan)
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

environment = os.getenv("APP_ENVIRONMENT", "dev")

app.include_router(health_router)
app.include_router(edit_router)
app.include_router(generate_router)
app.include_router(parse_router)
app.include_router(screening_questions_router)
app.include_router(parse_router_v2)
app.include_router(generate_router_v2)


if __name__ == "__main__":
    app_host = os.getenv("APP_HOST", "0.0.0.0")
    app_port = int(os.getenv("APP_PORT", "8080"))
    reload = True if environment == "dev" else False

    uvicorn.run(
        app="main:app",
        host=app_host,
        port=app_port,
        log_config=LOGGING_CONFIG,
        reload=reload,
    )
